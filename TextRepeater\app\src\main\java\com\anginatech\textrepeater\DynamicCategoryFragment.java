package com.anginatech.textrepeater;

import android.database.Cursor;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Toast;

import androidx.fragment.app.Fragment;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.anginatech.textrepeater.models.Category;

import java.util.ArrayList;
import java.util.List;

/**
 * Generic fragment for displaying any category content
 */
public class DynamicCategoryFragment extends Fragment {

    private static final String ARG_CATEGORY = "category";
    private static final String ARG_USE_LEGACY_DB = "use_legacy_db";

    private Category category;
    private boolean useLegacyDb = false;
    private DatabaseHelper legacyDatabaseHelper;
    private DynamicDatabaseHelper dynamicDatabaseHelper;
    private RecyclerView recyclerView;
    private SmsAdapter adapter;
    private List<String> smsList = new ArrayList<>();

    public static DynamicCategoryFragment newInstance(Category category, boolean useLegacyDb) {
        DynamicCategoryFragment fragment = new DynamicCategoryFragment();
        Bundle args = new Bundle();
        args.putSerializable(ARG_CATEGORY, category);
        args.putBoolean(ARG_USE_LEGACY_DB, useLegacyDb);
        fragment.setArguments(args);
        return fragment;
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        if (getArguments() != null) {
            category = (Category) getArguments().getSerializable(ARG_CATEGORY);
            useLegacyDb = getArguments().getBoolean(ARG_USE_LEGACY_DB, false);
        }
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        View myView = inflater.inflate(R.layout.fragment_dynamic_category, container, false);
        
        if (container != null) {
            container.removeAllViews();
        }

        // Initialize database helpers
        if (useLegacyDb) {
            legacyDatabaseHelper = new DatabaseHelper(getContext());
        } else {
            dynamicDatabaseHelper = new DynamicDatabaseHelper(getContext());
        }

        // Initialize RecyclerView
        recyclerView = myView.findViewById(R.id.recyclerView);
        recyclerView.setLayoutManager(new LinearLayoutManager(requireContext(), LinearLayoutManager.VERTICAL, false));
        recyclerView.setHasFixedSize(true);

        // Load messages for this category
        loadMessages();

        return myView;
    }

    private void loadMessages() {
        smsList.clear();
        
        try {
            if (useLegacyDb && legacyDatabaseHelper != null) {
                // Use legacy database for backward compatibility
                loadMessagesFromLegacyDb();
            } else if (dynamicDatabaseHelper != null) {
                // Use new dynamic database
                loadMessagesFromDynamicDb();
            }
            
            // Set up adapter
            adapter = new SmsAdapter(getContext(), smsList);
            recyclerView.setAdapter(adapter);
            
        } catch (Exception e) {
            Toast.makeText(getContext(), "Error loading messages: " + e.getMessage(), Toast.LENGTH_SHORT).show();
        }
    }

    private void loadMessagesFromLegacyDb() {
        if (category == null) return;
        
        String tableName = category.getTableName();
        Cursor cursor = legacyDatabaseHelper.getAllDatabyCatagory(tableName);
        
        if (cursor != null && cursor.moveToFirst()) {
            do {
                String message = cursor.getString(1);
                smsList.add(message);
            } while (cursor.moveToNext());
            cursor.close();
        } else {
            Toast.makeText(getContext(), "No messages found for " + category.getDisplayName(), Toast.LENGTH_SHORT).show();
        }
    }

    private void loadMessagesFromDynamicDb() {
        if (category == null) return;
        
        List<String> messages = dynamicDatabaseHelper.getMessagesAsStringList(category.getName());
        smsList.addAll(messages);
        
        if (messages.isEmpty()) {
            Toast.makeText(getContext(), "No messages found for " + category.getDisplayName(), Toast.LENGTH_SHORT).show();
        }
    }

    /**
     * Refresh the fragment data
     */
    public void refreshData() {
        if (recyclerView != null) {
            loadMessages();
        }
    }

    /**
     * Get the category for this fragment
     */
    public Category getCategory() {
        return category;
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        recyclerView = null;
        adapter = null;
    }
}
