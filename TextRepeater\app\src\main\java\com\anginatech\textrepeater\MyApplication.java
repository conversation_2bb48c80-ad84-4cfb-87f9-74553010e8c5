package com.anginatech.textrepeater;

import android.app.Application;
import android.util.Log;
import android.widget.Toast;

import com.android.volley.Request;
import com.android.volley.toolbox.JsonObjectRequest;
import com.android.volley.toolbox.Volley;
import com.anginatech.textrepeater.models.Category;
import com.anginatech.textrepeater.repository.TextRepeaterRepository;
import com.google.android.gms.ads.MobileAds;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

public class MyApplication extends Application {

    DatabaseHelper databaseHelper;
    DynamicDatabaseHelper dynamicDatabaseHelper;
    private AppOpenAdManager appOpenAdManager;
    private UserRegistrationHelper userRegistrationHelper;
    private TextRepeaterRepository repository;

    @Override
    public void onCreate() {
        super.onCreate();

        // Initialize Mobile Ads SDK (lightweight)
        MobileAds.initialize(this, initializationStatus -> {
            Log.d("MyApplication", "Mobile Ads SDK initialized");
        });

        // Initialize database helpers (lightweight)
        databaseHelper = new DatabaseHelper(this);
        dynamicDatabaseHelper = new DynamicDatabaseHelper(this);

        // Initialize App Open Ad Manager (lightweight)
        appOpenAdManager = new AppOpenAdManager(this);

        // Initialize User Registration Helper (lightweight)
        userRegistrationHelper = new UserRegistrationHelper(this);

        // Initialize modern repository (lightweight and fast)
        try {
            repository = TextRepeaterRepository.getInstance(this);
            Log.d("MyApplication", "✅ Modern repository initialized successfully");

            // Run API tests in debug mode
            if (Config.isDebugMode()) {
                com.anginatech.textrepeater.utils.ApiTestHelper.runAllTests(this);
            }
        } catch (Exception e) {
            Log.e("MyApplication", "❌ Error initializing modern repository: " + e.getMessage());
            e.printStackTrace();
        }

        // Perform heavy operations in background to speed up app startup
        performBackgroundInitialization();
    }

    /**
     * Perform all heavy operations in background to avoid blocking app startup
     * This makes the app start much faster and provides better user experience
     */
    private void performBackgroundInitialization() {
        new Thread(() -> {
            try {
                // Register user with admin panel (background)
                userRegistrationHelper.registerUser();

                // Fetch and store data using modern repository with caching
                fetchDataWithModernRepository();

                Log.d("MyApplication", "Background initialization completed successfully");
            } catch (Exception e) {
                Log.e("MyApplication", "Error in background initialization: " + e.getMessage());
            }
        }).start();

        // Initialize ads configuration on main thread (requires Handler)
        new android.os.Handler(android.os.Looper.getMainLooper()).post(() -> {
            try {
                AdsHelper.initializeAds(this);
                loadAppOpenAdConfig();
                Log.d("MyApplication", "Ads initialization completed successfully");
            } catch (Exception e) {
                Log.e("MyApplication", "Error in ads initialization: " + e.getMessage());
            }
        });
    }

    public AppOpenAdManager getAppOpenAdManager() {
        return appOpenAdManager;
    }

    public UserRegistrationHelper getUserRegistrationHelper() {
        return userRegistrationHelper;
    }

    public DynamicDatabaseHelper getDynamicDatabaseHelper() {
        return dynamicDatabaseHelper;
    }

    public TextRepeaterRepository getRepository() {
        return repository;
    }

    /**
     * Modern data fetching using Repository pattern with caching and performance optimizations
     * This replaces the slow Volley-based fetchAndStoreData() method
     */
    private void fetchDataWithModernRepository() {
        // Fetch text content with intelligent caching
        repository.fetchTextContent(new TextRepeaterRepository.RepositoryCallback<com.anginatech.textrepeater.models.TextContentResponse>() {
            @Override
            public void onSuccess(com.anginatech.textrepeater.models.TextContentResponse data) {
                // Store data in local database for offline access
                storeTextContentInDatabase(data);
                Log.d("MyApplication", "Text content fetched and stored using modern repository");
            }

            @Override
            public void onError(String error) {
                Log.e("MyApplication", "Error fetching text content: " + error);
                // Fallback to legacy method if modern approach fails
                fetchAndStoreData();
            }
        });

        // Fetch dynamic content with caching
        repository.fetchTextContent(new TextRepeaterRepository.RepositoryCallback<com.anginatech.textrepeater.models.TextContentResponse>() {
            @Override
            public void onSuccess(com.anginatech.textrepeater.models.TextContentResponse data) {
                // Store dynamic content
                storeDynamicContentInDatabase(data);
                Log.d("MyApplication", "Dynamic content fetched and stored using modern repository");
            }

            @Override
            public void onError(String error) {
                Log.e("MyApplication", "Error fetching dynamic content: " + error);
                // Fallback to legacy method
                fetchAndStoreDynamicData();
            }
        });
    }

    /**
     * Store text content in database with optimized batch operations
     */
    private void storeTextContentInDatabase(com.anginatech.textrepeater.models.TextContentResponse data) {
        try {
            // Store romantic messages
            if (data.getRomantic() != null) {
                for (com.anginatech.textrepeater.models.TextContentResponse.TextMessage message : data.getRomantic()) {
                    databaseHelper.insertOrUpdateDataRomantic(message.getId(), message.getMessage());
                }
            }

            // Store sad messages
            if (data.getSad() != null) {
                for (com.anginatech.textrepeater.models.TextContentResponse.TextMessage message : data.getSad()) {
                    databaseHelper.insertOrUpdateDataSad(message.getId(), message.getMessage());
                }
            }

            // Store funny messages
            if (data.getFunny() != null) {
                for (com.anginatech.textrepeater.models.TextContentResponse.TextMessage message : data.getFunny()) {
                    databaseHelper.insertOrUpdateDataFunny(message.getId(), message.getMessage());
                }
            }

            Log.d("MyApplication", "Text content stored in database successfully");
        } catch (Exception e) {
            Log.e("MyApplication", "Error storing text content: " + e.getMessage());
        }
    }

    /**
     * Store dynamic content in database with optimized operations
     */
    private void storeDynamicContentInDatabase(com.anginatech.textrepeater.models.TextContentResponse data) {
        try {
            // Store categories
            if (data.getCategories() != null) {
                for (Category category : data.getCategories()) {
                    dynamicDatabaseHelper.insertOrUpdateCategory(category);
                }
            }

            Log.d("MyApplication", "Dynamic content stored in database successfully");
        } catch (Exception e) {
            Log.e("MyApplication", "Error storing dynamic content: " + e.getMessage());
        }
    }

    public void fetchAndStoreData() {
        // Using config-based URL from admin panel API
        String url = Config.buildDirectApiUrl(Config.ENDPOINT_TEXT_CONTENT);

        JsonObjectRequest jsonObjectRequest = new JsonObjectRequest(
                Request.Method.GET,
                url,
                null,
                response -> {
                    try {
                        JSONArray romanticArray = response.getJSONArray("romantic");
                        for (int i = 0; i < romanticArray.length(); i++) {
                            JSONObject jsonObject = romanticArray.getJSONObject(i);
                            String id = jsonObject.getString("id");
                            String message = jsonObject.getString("message");
                            databaseHelper.insertOrUpdateDataRomantic( id, message);
                        }


                        JSONArray sadArray = null;
                        try {
                            sadArray = response.getJSONArray("sad");
                            for (int i = 0; i < sadArray.length(); i++) {
                                JSONObject jsonObject = sadArray.getJSONObject(i);
                                String id = jsonObject.getString("id");
                                String message = jsonObject.getString("message");
                                databaseHelper.insertOrUpdateDataSad(id, message);
                            }
                        } catch (JSONException e) {
                            throw new RuntimeException(e);
                        }

                        try {
                            JSONArray funnyArray = response.getJSONArray("funny");
                            for (int i = 0; i < funnyArray.length(); i++) {
                                JSONObject jsonObject = funnyArray.getJSONObject(i);
                                String id = jsonObject.getString("id");
                                String message = jsonObject.getString("message");
                                databaseHelper.insertOrUpdateDataFunny(id, message);
                            }
                        } catch (RuntimeException e) {
                            throw new RuntimeException(e);
                        }


                    } catch (JSONException e) {

                        e.printStackTrace();
                    }
                },
                error -> error.printStackTrace()
        );

        Volley.newRequestQueue(this).add(jsonObjectRequest);
    }

    /**
     * Load App Open Ad configuration from server using new API
     */
    private void loadAppOpenAdConfig() {
        ApiClient apiClient = ApiClient.getInstance(this);

        apiClient.fetchAdMobConfig(new ApiClient.AdMobConfigCallback() {
            @Override
            public void onSuccess(JSONObject config) {
                try {
                    String appOpenAdUnitId = config.optString("app_open_id", "");

                    if (!appOpenAdUnitId.isEmpty()) {
                        appOpenAdManager.setAppOpenAdUnitId(appOpenAdUnitId);

                        // Load the first app open ad
                        appOpenAdManager.loadAd(MyApplication.this);

                        Log.d("MyApplication", "App Open Ad Unit ID loaded from new API: " + appOpenAdUnitId);
                    } else {
                        Log.w("MyApplication", "No App Open Ad Unit ID found in config");
                    }
                } catch (Exception e) {
                    Log.e("MyApplication", "Error processing App Open Ad config: " + e.getMessage());
                }
            }

            @Override
            public void onError(String error) {
                Log.e("MyApplication", "Error loading App Open Ad config from new API: " + error);

                // Fallback to old API if new API fails
                loadAppOpenAdConfigFallback();
            }
        });
    }

    /**
     * Fallback method to load App Open Ad config from old API
     */
    private void loadAppOpenAdConfigFallback() {
        String url = Config.buildApiUrl(Config.ENDPOINT_ADS_CONFIG);

        JsonObjectRequest request = new JsonObjectRequest(Request.Method.GET, url, null,
                response -> {
                    try {
                        if (response.has("app_open_id") && !response.getString("app_open_id").isEmpty()) {
                            String appOpenAdUnitId = response.getString("app_open_id");
                            appOpenAdManager.setAppOpenAdUnitId(appOpenAdUnitId);

                            // Load the first app open ad
                            appOpenAdManager.loadAd(this);

                            Log.d("MyApplication", "App Open Ad Unit ID loaded from fallback API: " + appOpenAdUnitId);
                        }
                    } catch (JSONException e) {
                        Log.e("MyApplication", "Error parsing App Open Ad config from fallback: " + e.getMessage());
                    }
                },
                error -> {
                    Log.e("MyApplication", "Error loading App Open Ad config from fallback API: " + error.getMessage());
                });

        Volley.newRequestQueue(this).add(request);
    }

    /**
     * Fetch and store dynamic categories and content
     */
    public void fetchAndStoreDynamicData() {
        String url = Config.buildDirectApiUrl(Config.ENDPOINT_DYNAMIC_CONTENT);

        JsonObjectRequest jsonObjectRequest = new JsonObjectRequest(
                Request.Method.GET,
                url,
                null,
                response -> {
                    try {
                        // Parse categories
                        JSONArray categoriesArray = response.getJSONArray("categories");
                        for (int i = 0; i < categoriesArray.length(); i++) {
                            JSONObject categoryObj = categoriesArray.getJSONObject(i);

                            Category category = new Category();
                            category.setId(categoryObj.getInt("id"));
                            category.setName(categoryObj.getString("name"));
                            category.setDisplayName(categoryObj.getString("display_name"));
                            category.setDescription(categoryObj.optString("description", ""));
                            category.setSortOrder(categoryObj.optInt("sort_order", 0));

                            dynamicDatabaseHelper.insertOrUpdateCategory(category);
                        }

                        // Parse content
                        JSONObject contentObj = response.getJSONObject("content");
                        for (int i = 0; i < categoriesArray.length(); i++) {
                            JSONObject categoryObj = categoriesArray.getJSONObject(i);
                            String categoryName = categoryObj.getString("name");

                            if (contentObj.has(categoryName)) {
                                JSONArray messagesArray = contentObj.getJSONArray(categoryName);
                                for (int j = 0; j < messagesArray.length(); j++) {
                                    JSONObject messageObj = messagesArray.getJSONObject(j);
                                    String id = messageObj.getString("id");
                                    String message = messageObj.getString("message");

                                    dynamicDatabaseHelper.insertOrUpdateMessage(id, message, categoryName);
                                }
                            }
                        }

                        Log.d("MyApplication", "Dynamic data fetched and stored successfully");

                    } catch (JSONException e) {
                        Log.e("MyApplication", "Error parsing dynamic data: " + e.getMessage());
                        e.printStackTrace();
                    }
                },
                error -> {
                    Log.e("MyApplication", "Error fetching dynamic data: " + error.getMessage());
                    error.printStackTrace();
                }
        );

        Volley.newRequestQueue(this).add(jsonObjectRequest);
    }
}
