#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (mmap) failed to map 63963136 bytes for G1 virtual space
# Possible reasons:
#   The system is out of physical RAM or swap space
#   The process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Unscaled Compressed Oops mode in which the Java heap is
#     placed in the first 4GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 4GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (os_windows.cpp:3600), pid=15984, tid=10384
#
# JRE version: Java(TM) SE Runtime Environment (21.0.2+13) (build 21.0.2+13-LTS-58)
# Java VM: Java HotSpot(TM) 64-Bit Server VM (21.0.2+13-LTS-58, mixed mode, sharing, tiered, compressed oops, compressed class ptrs, g1 gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.api=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.util=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-opens=java.base/java.nio.charset=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED --add-opens=java.xml/javax.xml.namespace=ALL-UNNAMED -Xmx2048m -Dfile.encoding=UTF-8 -Duser.country=US -Duser.language=en -Duser.variant -javaagent:C:\Users\<USER>\.gradle\wrapper\dists\gradle-8.13-bin\5xuhj0ry160q40clulazy9h7d\gradle-8.13\lib\agents\gradle-instrumentation-agent-8.13.jar org.gradle.launcher.daemon.bootstrap.GradleDaemon 8.13

Host: AMD Ryzen 5 3400G with Radeon Vega Graphics    , 8 cores, 21G,  Windows 10 , 64 bit Build 19041 (10.0.19041.5912)
Time: Tue May 27 18:00:10 2025 Bangladesh Standard Time elapsed time: 20.090000 seconds (0d 0h 0m 20s)

---------------  T H R E A D  ---------------

Current thread (0x000001d44f1d7670):  VMThread "VM Thread"          [id=10384, stack(0x0000004845500000,0x0000004845600000) (1024K)]

Stack: [0x0000004845500000,0x0000004845600000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x6cade9]
V  [jvm.dll+0x8569c1]
V  [jvm.dll+0x858d2e]
V  [jvm.dll+0x859413]
V  [jvm.dll+0x280e56]
V  [jvm.dll+0x6c74d5]
V  [jvm.dll+0x6bbeca]
V  [jvm.dll+0x355bca]
V  [jvm.dll+0x35d816]
V  [jvm.dll+0x3ae67e]
V  [jvm.dll+0x3ae928]
V  [jvm.dll+0x3295dc]
V  [jvm.dll+0x329667]
V  [jvm.dll+0x36df1c]
V  [jvm.dll+0x36c71d]
V  [jvm.dll+0x328d51]
V  [jvm.dll+0x36be2a]
V  [jvm.dll+0x85ef18]
V  [jvm.dll+0x860014]
V  [jvm.dll+0x860550]
V  [jvm.dll+0x8607f3]
V  [jvm.dll+0x7ff568]
V  [jvm.dll+0x6c953d]
C  [ucrtbase.dll+0x21bb2]
C  [KERNEL32.DLL+0x17374]
C  [ntdll.dll+0x4cc91]

VM_Operation (0x000000484a0f6c70): G1CollectForAllocation, mode: safepoint, requested by thread 0x000001d49adbb2d0


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x000001d49a522a70, length=138, elements={
0x000001d4327e6030, 0x000001d44f1f2340, 0x000001d44f1f30b0, 0x000001d44f1f4200,
0x000001d44f1f5dd0, 0x000001d44f1f6b90, 0x000001d44f1f75f0, 0x000001d44f203bf0,
0x000001d44f2b2110, 0x000001d44f38b3b0, 0x000001d44f50fcb0, 0x000001d4961ee880,
0x000001d4963450c0, 0x000001d49627a700, 0x000001d4963879d0, 0x000001d496388040,
0x000001d496398470, 0x000001d49639db30, 0x000001d49639c0f0, 0x000001d49639ba60,
0x000001d49639c780, 0x000001d49639b3d0, 0x000001d49639ce10, 0x000001d49639d4a0,
0x000001d49639ad40, 0x000001d496a443a0, 0x000001d496a46b00, 0x000001d496a42960,
0x000001d496a422d0, 0x000001d496a450c0, 0x000001d496a43d10, 0x000001d496a45750,
0x000001d496a45de0, 0x000001d496a41c40, 0x000001d496a47190, 0x000001d496a40f20,
0x000001d496a47820, 0x000001d496a43680, 0x000001d496a47eb0, 0x000001d496a415b0,
0x000001d4954745f0, 0x000001d495479b40, 0x000001d495474c80, 0x000001d495475310,
0x000001d495476030, 0x000001d4954766c0, 0x000001d4954759a0, 0x000001d495476d50,
0x000001d4954773e0, 0x000001d4954794b0, 0x000001d495478790, 0x000001d49547a1d0,
0x000001d495473f60, 0x000001d49547a860, 0x000001d495477a70, 0x000001d4954738d0,
0x000001d495478100, 0x000001d49547aef0, 0x000001d49ba74de0, 0x000001d49ba740c0,
0x000001d49ba71ff0, 0x000001d49adbfb00, 0x000001d49adbc680, 0x000001d49adbcd10,
0x000001d49adbb960, 0x000001d49adbb2d0, 0x000001d49adc0190, 0x000001d49adc0820,
0x000001d49adbe750, 0x000001d49adbd3a0, 0x000001d49adc0eb0, 0x000001d49adb9f20,
0x000001d49adc1540, 0x000001d49adba5b0, 0x000001d49adbbff0, 0x000001d49a91d5d0,
0x000001d49a91dc60, 0x000001d49a918da0, 0x000001d49a919430, 0x000001d49a919ac0,
0x000001d49a91a150, 0x000001d49a918080, 0x000001d49a91a7e0, 0x000001d49a91ae70,
0x000001d49a91b500, 0x000001d49a918710, 0x000001d49a91e2f0, 0x000001d49a91e980,
0x000001d49a91bb90, 0x000001d49a91c220, 0x000001d49a91f010, 0x000001d49a91c8b0,
0x000001d49a91f6a0, 0x000001d49a91cf40, 0x000001d49ba74750, 0x000001d49ba705b0,
0x000001d49c44d220, 0x000001d49c44b7e0, 0x000001d49c44c500, 0x000001d49c44cb90,
0x000001d49c449710, 0x000001d49c448360, 0x000001d49c7d1920, 0x000001d49c7d1fb0,
0x000001d49c7d2cd0, 0x000001d49c7cde10, 0x000001d49c7d2640, 0x000001d49c7cd780,
0x000001d49c7ceb30, 0x000001d49c7ce4a0, 0x000001d49b637e30, 0x000001d49c7d39f0,
0x000001d49b637760, 0x000001d49c7d4080, 0x000001d49c7d4710, 0x000001d49c7d0570,
0x000001d49c7d1290, 0x000001d49c44e5d0, 0x000001d49c449da0, 0x000001d49c4489f0,
0x000001d49c44aac0, 0x000001d49c44d8b0, 0x000001d49c44df40, 0x000001d49c44ec60,
0x000001d49c447cd0, 0x000001d49c44f2f0, 0x000001d49c44be70, 0x000001d49c449080,
0x000001d49c44a430, 0x000001d49c44b150, 0x000001d49adbede0, 0x000001d49adbe0c0,
0x000001d49adbac40, 0x000001d49adbda30, 0x000001d49adbf470, 0x000001d49ba70c40,
0x000001d49ba6eb70, 0x000001d49ba76190
}

Java Threads: ( => current thread )
  0x000001d4327e6030 JavaThread "main"                              [_thread_blocked, id=10740, stack(0x0000004844e00000,0x0000004844f00000) (1024K)]
  0x000001d44f1f2340 JavaThread "Reference Handler"          daemon [_thread_blocked, id=15920, stack(0x0000004845600000,0x0000004845700000) (1024K)]
  0x000001d44f1f30b0 JavaThread "Finalizer"                  daemon [_thread_blocked, id=5300, stack(0x0000004845700000,0x0000004845800000) (1024K)]
  0x000001d44f1f4200 JavaThread "Signal Dispatcher"          daemon [_thread_blocked, id=16032, stack(0x0000004845800000,0x0000004845900000) (1024K)]
  0x000001d44f1f5dd0 JavaThread "Attach Listener"            daemon [_thread_blocked, id=18784, stack(0x0000004845900000,0x0000004845a00000) (1024K)]
  0x000001d44f1f6b90 JavaThread "Service Thread"             daemon [_thread_blocked, id=16256, stack(0x0000004845a00000,0x0000004845b00000) (1024K)]
  0x000001d44f1f75f0 JavaThread "Monitor Deflation Thread"   daemon [_thread_blocked, id=13472, stack(0x0000004845b00000,0x0000004845c00000) (1024K)]
  0x000001d44f203bf0 JavaThread "C2 CompilerThread0"         daemon [_thread_in_native, id=13408, stack(0x0000004845c00000,0x0000004845d00000) (1024K)]
  0x000001d44f2b2110 JavaThread "C1 CompilerThread0"         daemon [_thread_blocked, id=18368, stack(0x0000004845d00000,0x0000004845e00000) (1024K)]
  0x000001d44f38b3b0 JavaThread "Common-Cleaner"             daemon [_thread_blocked, id=17428, stack(0x0000004845e00000,0x0000004845f00000) (1024K)]
  0x000001d44f50fcb0 JavaThread "Notification Thread"        daemon [_thread_blocked, id=16928, stack(0x0000004845f00000,0x0000004846000000) (1024K)]
  0x000001d4961ee880 JavaThread "Daemon health stats"               [_thread_blocked, id=18476, stack(0x0000004846100000,0x0000004846200000) (1024K)]
  0x000001d4963450c0 JavaThread "Incoming local TCP Connector on port 50707"        [_thread_in_native, id=17128, stack(0x0000004846900000,0x0000004846a00000) (1024K)]
  0x000001d49627a700 JavaThread "Daemon periodic checks"            [_thread_blocked, id=10336, stack(0x0000004846a00000,0x0000004846b00000) (1024K)]
  0x000001d4963879d0 JavaThread "Daemon"                            [_thread_blocked, id=18632, stack(0x0000004846b00000,0x0000004846c00000) (1024K)]
  0x000001d496388040 JavaThread "Handler for socket connection from /127.0.0.1:50707 to /127.0.0.1:50708"        [_thread_in_native, id=13572, stack(0x0000004846c00000,0x0000004846d00000) (1024K)]
  0x000001d496398470 JavaThread "Cancel handler"                    [_thread_blocked, id=11256, stack(0x0000004846d00000,0x0000004846e00000) (1024K)]
  0x000001d49639db30 JavaThread "Daemon worker"                     [_thread_blocked, id=18132, stack(0x0000004846e00000,0x0000004846f00000) (1024K)]
  0x000001d49639c0f0 JavaThread "Asynchronous log dispatcher for DefaultDaemonConnection: socket connection from /127.0.0.1:50707 to /127.0.0.1:50708"        [_thread_blocked, id=8224, stack(0x0000004846000000,0x0000004846100000) (1024K)]
  0x000001d49639ba60 JavaThread "Stdin handler"                     [_thread_blocked, id=17404, stack(0x0000004846f00000,0x0000004847000000) (1024K)]
  0x000001d49639c780 JavaThread "Daemon client event forwarder"        [_thread_blocked, id=10644, stack(0x0000004847000000,0x0000004847100000) (1024K)]
  0x000001d49639b3d0 JavaThread "Cache worker for journal cache (C:\Users\<USER>\.gradle\caches\journal-1)"        [_thread_blocked, id=3376, stack(0x0000004847200000,0x0000004847300000) (1024K)]
  0x000001d49639ce10 JavaThread "File lock request listener"        [_thread_in_native, id=1156, stack(0x0000004847300000,0x0000004847400000) (1024K)]
  0x000001d49639d4a0 JavaThread "Cache worker for file hash cache (C:\Users\<USER>\.gradle\caches\8.13\fileHashes)"        [_thread_blocked, id=5496, stack(0x0000004847400000,0x0000004847500000) (1024K)]
  0x000001d49639ad40 JavaThread "Cache worker for file hash cache (C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\.gradle\8.13\fileHashes)"        [_thread_blocked, id=1392, stack(0x0000004847700000,0x0000004847800000) (1024K)]
  0x000001d496a443a0 JavaThread "Cache worker for Build Output Cleanup Cache (C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\.gradle\buildOutputCleanup)"        [_thread_blocked, id=14376, stack(0x0000004847800000,0x0000004847900000) (1024K)]
  0x000001d496a46b00 JavaThread "File watcher server"        daemon [_thread_blocked, id=17440, stack(0x0000004847900000,0x0000004847a00000) (1024K)]
  0x000001d496a42960 JavaThread "File watcher consumer"      daemon [_thread_blocked, id=2360, stack(0x0000004847a00000,0x0000004847b00000) (1024K)]
  0x000001d496a422d0 JavaThread "jar transforms"                    [_thread_blocked, id=7708, stack(0x0000004847b00000,0x0000004847c00000) (1024K)]
  0x000001d496a450c0 JavaThread "Cache worker for checksums cache (C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\.gradle\8.13\checksums)"        [_thread_blocked, id=220, stack(0x0000004847c00000,0x0000004847d00000) (1024K)]
  0x000001d496a43d10 JavaThread "Cache worker for file content cache (C:\Users\<USER>\.gradle\caches\8.13\fileContent)"        [_thread_blocked, id=11236, stack(0x0000004847d00000,0x0000004847e00000) (1024K)]
  0x000001d496a45750 JavaThread "Cache worker for cache directory md-supplier (C:\Users\<USER>\.gradle\caches\8.13\md-supplier)"        [_thread_blocked, id=7700, stack(0x0000004847e00000,0x0000004847f00000) (1024K)]
  0x000001d496a45de0 JavaThread "Cache worker for cache directory md-rule (C:\Users\<USER>\.gradle\caches\8.13\md-rule)"        [_thread_blocked, id=1428, stack(0x0000004847f00000,0x0000004848000000) (1024K)]
  0x000001d496a41c40 JavaThread "jar transforms Thread 2"           [_thread_blocked, id=9768, stack(0x0000004848000000,0x0000004848100000) (1024K)]
  0x000001d496a47190 JavaThread "jar transforms Thread 3"           [_thread_blocked, id=14652, stack(0x0000004848100000,0x0000004848200000) (1024K)]
  0x000001d496a40f20 JavaThread "Unconstrained build operations"        [_thread_blocked, id=15176, stack(0x0000004848200000,0x0000004848300000) (1024K)]
  0x000001d496a47820 JavaThread "Unconstrained build operations Thread 2"        [_thread_blocked, id=7652, stack(0x0000004848300000,0x0000004848400000) (1024K)]
  0x000001d496a43680 JavaThread "Unconstrained build operations Thread 3"        [_thread_blocked, id=9904, stack(0x0000004848400000,0x0000004848500000) (1024K)]
  0x000001d496a47eb0 JavaThread "Unconstrained build operations Thread 4"        [_thread_blocked, id=2772, stack(0x0000004848500000,0x0000004848600000) (1024K)]
  0x000001d496a415b0 JavaThread "Unconstrained build operations Thread 5"        [_thread_blocked, id=5708, stack(0x0000004848600000,0x0000004848700000) (1024K)]
  0x000001d4954745f0 JavaThread "Unconstrained build operations Thread 6"        [_thread_blocked, id=8668, stack(0x0000004848700000,0x0000004848800000) (1024K)]
  0x000001d495479b40 JavaThread "Unconstrained build operations Thread 7"        [_thread_blocked, id=17172, stack(0x0000004848800000,0x0000004848900000) (1024K)]
  0x000001d495474c80 JavaThread "Unconstrained build operations Thread 8"        [_thread_blocked, id=15244, stack(0x0000004848900000,0x0000004848a00000) (1024K)]
  0x000001d495475310 JavaThread "Unconstrained build operations Thread 9"        [_thread_blocked, id=18468, stack(0x0000004848a00000,0x0000004848b00000) (1024K)]
  0x000001d495476030 JavaThread "Unconstrained build operations Thread 10"        [_thread_blocked, id=18348, stack(0x0000004848b00000,0x0000004848c00000) (1024K)]
  0x000001d4954766c0 JavaThread "Unconstrained build operations Thread 11"        [_thread_blocked, id=10820, stack(0x0000004848c00000,0x0000004848d00000) (1024K)]
  0x000001d4954759a0 JavaThread "Unconstrained build operations Thread 12"        [_thread_blocked, id=15948, stack(0x0000004848d00000,0x0000004848e00000) (1024K)]
  0x000001d495476d50 JavaThread "Unconstrained build operations Thread 13"        [_thread_blocked, id=4776, stack(0x0000004848e00000,0x0000004848f00000) (1024K)]
  0x000001d4954773e0 JavaThread "Unconstrained build operations Thread 14"        [_thread_blocked, id=17124, stack(0x0000004848f00000,0x0000004849000000) (1024K)]
  0x000001d4954794b0 JavaThread "Unconstrained build operations Thread 15"        [_thread_blocked, id=10912, stack(0x0000004849000000,0x0000004849100000) (1024K)]
  0x000001d495478790 JavaThread "Unconstrained build operations Thread 16"        [_thread_blocked, id=3652, stack(0x0000004849100000,0x0000004849200000) (1024K)]
  0x000001d49547a1d0 JavaThread "Unconstrained build operations Thread 17"        [_thread_blocked, id=3520, stack(0x0000004849200000,0x0000004849300000) (1024K)]
  0x000001d495473f60 JavaThread "Unconstrained build operations Thread 18"        [_thread_blocked, id=9532, stack(0x0000004849300000,0x0000004849400000) (1024K)]
  0x000001d49547a860 JavaThread "Unconstrained build operations Thread 19"        [_thread_blocked, id=16388, stack(0x0000004849400000,0x0000004849500000) (1024K)]
  0x000001d495477a70 JavaThread "Unconstrained build operations Thread 20"        [_thread_blocked, id=1332, stack(0x0000004849500000,0x0000004849600000) (1024K)]
  0x000001d4954738d0 JavaThread "Unconstrained build operations Thread 21"        [_thread_blocked, id=5360, stack(0x0000004849600000,0x0000004849700000) (1024K)]
  0x000001d495478100 JavaThread "jar transforms Thread 4"           [_thread_blocked, id=15796, stack(0x0000004849700000,0x0000004849800000) (1024K)]
  0x000001d49547aef0 JavaThread "jar transforms Thread 5"           [_thread_blocked, id=15368, stack(0x0000004849800000,0x0000004849900000) (1024K)]
  0x000001d49ba74de0 JavaThread "Memory manager"                    [_thread_blocked, id=19000, stack(0x0000004847500000,0x0000004847600000) (1024K)]
  0x000001d49ba740c0 JavaThread "Problems report writer"            [_thread_blocked, id=9100, stack(0x0000004849a00000,0x0000004849b00000) (1024K)]
  0x000001d49ba71ff0 JavaThread "jar transforms Thread 6"           [_thread_blocked, id=17532, stack(0x0000004849b00000,0x0000004849c00000) (1024K)]
  0x000001d49adbfb00 JavaThread "pool-3-thread-1"                   [_thread_blocked, id=19108, stack(0x0000004849c00000,0x0000004849d00000) (1024K)]
  0x000001d49adbc680 JavaThread "build event listener"              [_thread_blocked, id=15356, stack(0x0000004849d00000,0x0000004849e00000) (1024K)]
  0x000001d49adbcd10 JavaThread "included builds"                   [_thread_blocked, id=18344, stack(0x0000004849e00000,0x0000004849f00000) (1024K)]
  0x000001d49adbb960 JavaThread "Execution worker"                  [_thread_blocked, id=6544, stack(0x0000004849f00000,0x000000484a000000) (1024K)]
  0x000001d49adbb2d0 JavaThread "Execution worker Thread 2"         [_thread_blocked, id=6336, stack(0x000000484a000000,0x000000484a100000) (1024K)]
  0x000001d49adc0190 JavaThread "Execution worker Thread 3"         [_thread_blocked, id=11620, stack(0x000000484a100000,0x000000484a200000) (1024K)]
  0x000001d49adc0820 JavaThread "Execution worker Thread 4"         [_thread_blocked, id=15520, stack(0x000000484a200000,0x000000484a300000) (1024K)]
  0x000001d49adbe750 JavaThread "Execution worker Thread 5"         [_thread_blocked, id=8812, stack(0x000000484a300000,0x000000484a400000) (1024K)]
  0x000001d49adbd3a0 JavaThread "Execution worker Thread 6"         [_thread_blocked, id=17388, stack(0x000000484a400000,0x000000484a500000) (1024K)]
  0x000001d49adc0eb0 JavaThread "Execution worker Thread 7"         [_thread_blocked, id=13448, stack(0x000000484a500000,0x000000484a600000) (1024K)]
  0x000001d49adb9f20 JavaThread "Cache worker for execution history cache (C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\.gradle\8.13\executionHistory)"        [_thread_blocked, id=6528, stack(0x000000484a600000,0x000000484a700000) (1024K)]
  0x000001d49adc1540 JavaThread "Unconstrained build operations Thread 22"        [_thread_blocked, id=15268, stack(0x000000484a700000,0x000000484a800000) (1024K)]
  0x000001d49adba5b0 JavaThread "Unconstrained build operations Thread 23"        [_thread_blocked, id=10876, stack(0x000000484a800000,0x000000484a900000) (1024K)]
  0x000001d49adbbff0 JavaThread "Unconstrained build operations Thread 24"        [_thread_blocked, id=14888, stack(0x000000484a900000,0x000000484aa00000) (1024K)]
  0x000001d49a91d5d0 JavaThread "Unconstrained build operations Thread 25"        [_thread_blocked, id=17976, stack(0x000000484aa00000,0x000000484ab00000) (1024K)]
  0x000001d49a91dc60 JavaThread "Unconstrained build operations Thread 26"        [_thread_blocked, id=10892, stack(0x000000484ab00000,0x000000484ac00000) (1024K)]
  0x000001d49a918da0 JavaThread "Unconstrained build operations Thread 27"        [_thread_blocked, id=19252, stack(0x000000484ac00000,0x000000484ad00000) (1024K)]
  0x000001d49a919430 JavaThread "Unconstrained build operations Thread 28"        [_thread_blocked, id=17000, stack(0x000000484ad00000,0x000000484ae00000) (1024K)]
  0x000001d49a919ac0 JavaThread "Unconstrained build operations Thread 29"        [_thread_blocked, id=9036, stack(0x000000484ae00000,0x000000484af00000) (1024K)]
  0x000001d49a91a150 JavaThread "Unconstrained build operations Thread 30"        [_thread_blocked, id=704, stack(0x000000484af00000,0x000000484b000000) (1024K)]
  0x000001d49a918080 JavaThread "Unconstrained build operations Thread 31"        [_thread_blocked, id=16576, stack(0x000000484b000000,0x000000484b100000) (1024K)]
  0x000001d49a91a7e0 JavaThread "Unconstrained build operations Thread 32"        [_thread_blocked, id=14184, stack(0x000000484b100000,0x000000484b200000) (1024K)]
  0x000001d49a91ae70 JavaThread "Unconstrained build operations Thread 33"        [_thread_blocked, id=16184, stack(0x000000484b200000,0x000000484b300000) (1024K)]
  0x000001d49a91b500 JavaThread "Unconstrained build operations Thread 34"        [_thread_blocked, id=4276, stack(0x000000484b300000,0x000000484b400000) (1024K)]
  0x000001d49a918710 JavaThread "Unconstrained build operations Thread 35"        [_thread_blocked, id=9792, stack(0x000000484b400000,0x000000484b500000) (1024K)]
  0x000001d49a91e2f0 JavaThread "Unconstrained build operations Thread 36"        [_thread_blocked, id=17552, stack(0x000000484b500000,0x000000484b600000) (1024K)]
  0x000001d49a91e980 JavaThread "Unconstrained build operations Thread 37"        [_thread_blocked, id=18988, stack(0x000000484b600000,0x000000484b700000) (1024K)]
  0x000001d49a91bb90 JavaThread "Unconstrained build operations Thread 38"        [_thread_blocked, id=1260, stack(0x000000484b700000,0x000000484b800000) (1024K)]
  0x000001d49a91c220 JavaThread "Unconstrained build operations Thread 39"        [_thread_blocked, id=9712, stack(0x000000484b800000,0x000000484b900000) (1024K)]
  0x000001d49a91f010 JavaThread "Unconstrained build operations Thread 40"        [_thread_blocked, id=14864, stack(0x000000484b900000,0x000000484ba00000) (1024K)]
  0x000001d49a91c8b0 JavaThread "Unconstrained build operations Thread 41"        [_thread_blocked, id=18616, stack(0x000000484ba00000,0x000000484bb00000) (1024K)]
  0x000001d49a91f6a0 JavaThread "Unconstrained build operations Thread 42"        [_thread_blocked, id=10904, stack(0x000000484bb00000,0x000000484bc00000) (1024K)]
  0x000001d49a91cf40 JavaThread "Unconstrained build operations Thread 43"        [_thread_blocked, id=16868, stack(0x000000484bc00000,0x000000484bd00000) (1024K)]
  0x000001d49ba74750 JavaThread "Unconstrained build operations Thread 44"        [_thread_blocked, id=14340, stack(0x000000484bd00000,0x000000484be00000) (1024K)]
  0x000001d49ba705b0 JavaThread "Unconstrained build operations Thread 45"        [_thread_blocked, id=18296, stack(0x000000484be00000,0x000000484bf00000) (1024K)]
  0x000001d49c44d220 JavaThread "Unconstrained build operations Thread 46"        [_thread_blocked, id=10392, stack(0x000000484bf00000,0x000000484c000000) (1024K)]
  0x000001d49c44b7e0 JavaThread "Unconstrained build operations Thread 47"        [_thread_blocked, id=11112, stack(0x000000484c000000,0x000000484c100000) (1024K)]
  0x000001d49c44c500 JavaThread "Unconstrained build operations Thread 48"        [_thread_blocked, id=18584, stack(0x000000484c100000,0x000000484c200000) (1024K)]
  0x000001d49c44cb90 JavaThread "Unconstrained build operations Thread 49"        [_thread_blocked, id=18300, stack(0x000000484c200000,0x000000484c300000) (1024K)]
  0x000001d49c449710 JavaThread "Unconstrained build operations Thread 50"        [_thread_blocked, id=3560, stack(0x000000484c300000,0x000000484c400000) (1024K)]
  0x000001d49c448360 JavaThread "Unconstrained build operations Thread 51"        [_thread_blocked, id=9148, stack(0x000000484c400000,0x000000484c500000) (1024K)]
  0x000001d49c7d1920 JavaThread "Unconstrained build operations Thread 52"        [_thread_blocked, id=15972, stack(0x000000484c500000,0x000000484c600000) (1024K)]
  0x000001d49c7d1fb0 JavaThread "Unconstrained build operations Thread 53"        [_thread_blocked, id=7636, stack(0x000000484c600000,0x000000484c700000) (1024K)]
  0x000001d49c7d2cd0 JavaThread "Unconstrained build operations Thread 54"        [_thread_blocked, id=6332, stack(0x000000484c700000,0x000000484c800000) (1024K)]
  0x000001d49c7cde10 JavaThread "Unconstrained build operations Thread 55"        [_thread_blocked, id=17536, stack(0x000000484c800000,0x000000484c900000) (1024K)]
  0x000001d49c7d2640 JavaThread "Unconstrained build operations Thread 56"        [_thread_blocked, id=14960, stack(0x000000484c900000,0x000000484ca00000) (1024K)]
  0x000001d49c7cd780 JavaThread "Unconstrained build operations Thread 57"        [_thread_blocked, id=11648, stack(0x000000484ca00000,0x000000484cb00000) (1024K)]
  0x000001d49c7ceb30 JavaThread "Unconstrained build operations Thread 58"        [_thread_blocked, id=14276, stack(0x000000484cb00000,0x000000484cc00000) (1024K)]
  0x000001d49c7ce4a0 JavaThread "Unconstrained build operations Thread 59"        [_thread_blocked, id=19220, stack(0x000000484cc00000,0x000000484cd00000) (1024K)]
  0x000001d49b637e30 JavaThread "C2 CompilerThread1"         daemon [_thread_blocked, id=11420, stack(0x0000004847100000,0x0000004847200000) (1024K)]
  0x000001d49c7d39f0 JavaThread "WorkerExecutor Queue"              [_thread_blocked, id=11348, stack(0x0000004849900000,0x0000004849a00000) (1024K)]
  0x000001d49b637760 JavaThread "C2 CompilerThread2"         daemon [_thread_in_native, id=16696, stack(0x000000484cd00000,0x000000484ce00000) (1024K)]
  0x000001d49c7d4080 JavaThread "Unconstrained build operations Thread 60"        [_thread_blocked, id=8024, stack(0x000000484ce00000,0x000000484cf00000) (1024K)]
  0x000001d49c7d4710 JavaThread "Unconstrained build operations Thread 61"        [_thread_blocked, id=18748, stack(0x000000484cf00000,0x000000484d000000) (1024K)]
  0x000001d49c7d0570 JavaThread "Unconstrained build operations Thread 62"        [_thread_blocked, id=7916, stack(0x000000484d000000,0x000000484d100000) (1024K)]
  0x000001d49c7d1290 JavaThread "Unconstrained build operations Thread 63"        [_thread_blocked, id=16000, stack(0x000000484d100000,0x000000484d200000) (1024K)]
  0x000001d49c44e5d0 JavaThread "Unconstrained build operations Thread 64"        [_thread_blocked, id=10212, stack(0x000000484d200000,0x000000484d300000) (1024K)]
  0x000001d49c449da0 JavaThread "Unconstrained build operations Thread 65"        [_thread_blocked, id=16968, stack(0x000000484d300000,0x000000484d400000) (1024K)]
  0x000001d49c4489f0 JavaThread "Unconstrained build operations Thread 66"        [_thread_blocked, id=144, stack(0x000000484d400000,0x000000484d500000) (1024K)]
  0x000001d49c44aac0 JavaThread "Unconstrained build operations Thread 67"        [_thread_blocked, id=17904, stack(0x000000484d500000,0x000000484d600000) (1024K)]
  0x000001d49c44d8b0 JavaThread "Unconstrained build operations Thread 68"        [_thread_blocked, id=1884, stack(0x000000484d600000,0x000000484d700000) (1024K)]
  0x000001d49c44df40 JavaThread "Unconstrained build operations Thread 69"        [_thread_blocked, id=16844, stack(0x000000484d700000,0x000000484d800000) (1024K)]
  0x000001d49c44ec60 JavaThread "Unconstrained build operations Thread 70"        [_thread_blocked, id=6060, stack(0x000000484d800000,0x000000484d900000) (1024K)]
  0x000001d49c447cd0 JavaThread "Unconstrained build operations Thread 71"        [_thread_blocked, id=17572, stack(0x000000484d900000,0x000000484da00000) (1024K)]
  0x000001d49c44f2f0 JavaThread "Unconstrained build operations Thread 72"        [_thread_blocked, id=15380, stack(0x000000484da00000,0x000000484db00000) (1024K)]
  0x000001d49c44be70 JavaThread "Unconstrained build operations Thread 73"        [_thread_blocked, id=1828, stack(0x000000484db00000,0x000000484dc00000) (1024K)]
  0x000001d49c449080 JavaThread "Unconstrained build operations Thread 74"        [_thread_blocked, id=15744, stack(0x000000484dc00000,0x000000484dd00000) (1024K)]
  0x000001d49c44a430 JavaThread "WorkerExecutor Queue Thread 2"        [_thread_blocked, id=10924, stack(0x000000484dd00000,0x000000484de00000) (1024K)]
  0x000001d49c44b150 JavaThread "Unconstrained build operations Thread 75"        [_thread_blocked, id=17040, stack(0x000000484de00000,0x000000484df00000) (1024K)]
  0x000001d49adbede0 JavaThread "Unconstrained build operations Thread 76"        [_thread_blocked, id=8716, stack(0x000000484df00000,0x000000484e000000) (1024K)]
  0x000001d49adbe0c0 JavaThread "Unconstrained build operations Thread 77"        [_thread_blocked, id=7036, stack(0x000000484e000000,0x000000484e100000) (1024K)]
  0x000001d49adbac40 JavaThread "Unconstrained build operations Thread 78"        [_thread_blocked, id=8984, stack(0x000000484e100000,0x000000484e200000) (1024K)]
  0x000001d49adbda30 JavaThread "Unconstrained build operations Thread 79"        [_thread_blocked, id=14656, stack(0x000000484e200000,0x000000484e300000) (1024K)]
  0x000001d49adbf470 JavaThread "Unconstrained build operations Thread 80"        [_thread_blocked, id=552, stack(0x000000484e300000,0x000000484e400000) (1024K)]
  0x000001d49ba70c40 JavaThread "pool-4-thread-1"                   [_thread_blocked, id=18548, stack(0x000000484e400000,0x000000484e500000) (1024K)]
  0x000001d49ba6eb70 JavaThread "stderr"                            [_thread_in_native, id=15628, stack(0x000000484e500000,0x000000484e600000) (1024K)]
  0x000001d49ba76190 JavaThread "stdout"                            [_thread_in_native, id=5372, stack(0x000000484e600000,0x000000484e700000) (1024K)]
Total: 138

Other Threads:
=>0x000001d44f1d7670 VMThread "VM Thread"                           [id=10384, stack(0x0000004845500000,0x0000004845600000) (1024K)]
  0x000001d44f1c1690 WatcherThread "VM Periodic Task Thread"        [id=18912, stack(0x0000004845400000,0x0000004845500000) (1024K)]
  0x000001d43283f740 WorkerThread "GC Thread#0"                     [id=4648, stack(0x0000004844f00000,0x0000004845000000) (1024K)]
  0x000001d44fb438e0 WorkerThread "GC Thread#1"                     [id=9040, stack(0x0000004846200000,0x0000004846300000) (1024K)]
  0x000001d44f9fdba0 WorkerThread "GC Thread#2"                     [id=14080, stack(0x0000004846300000,0x0000004846400000) (1024K)]
  0x000001d44f9fdf40 WorkerThread "GC Thread#3"                     [id=18256, stack(0x0000004846400000,0x0000004846500000) (1024K)]
  0x000001d44f99b3a0 WorkerThread "GC Thread#4"                     [id=18308, stack(0x0000004846500000,0x0000004846600000) (1024K)]
  0x000001d44f99b740 WorkerThread "GC Thread#5"                     [id=9676, stack(0x0000004846600000,0x0000004846700000) (1024K)]
  0x000001d44f99bae0 WorkerThread "GC Thread#6"                     [id=1784, stack(0x0000004846700000,0x0000004846800000) (1024K)]
  0x000001d44f99be80 WorkerThread "GC Thread#7"                     [id=18604, stack(0x0000004846800000,0x0000004846900000) (1024K)]
  0x000001d4328507d0 ConcurrentGCThread "G1 Main Marker"            [id=17788, stack(0x0000004845000000,0x0000004845100000) (1024K)]
  0x000001d432851970 WorkerThread "G1 Conc#0"                       [id=17460, stack(0x0000004845100000,0x0000004845200000) (1024K)]
  0x000001d496559330 WorkerThread "G1 Conc#1"                       [id=18812, stack(0x0000004847600000,0x0000004847700000) (1024K)]
  0x000001d44f112280 ConcurrentGCThread "G1 Refine#0"               [id=18492, stack(0x0000004845200000,0x0000004845300000) (1024K)]
  0x000001d44f112c00 ConcurrentGCThread "G1 Service"                [id=16096, stack(0x0000004845300000,0x0000004845400000) (1024K)]
Total: 15

Threads with active compile tasks:
C2 CompilerThread0    20145 15442 %     4       com.android.tools.r8.internal.Vd::a @ 2762 (6886 bytes)
C2 CompilerThread1    20145 15704       4       com.android.tools.r8.graph.t4::a (31 bytes)
C2 CompilerThread2    20145 15311       4       com.android.tools.r8.internal.Vd::a (4363 bytes)
Total: 3

VM state: at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread:  ([mutex/lock_event])
[0x00007ff88abfcd68] Threads_lock - owner thread: 0x000001d44f1d7670
[0x00007ff88abfce68] Heap_lock - owner thread: 0x000001d49adbb2d0

Heap address: 0x0000000080000000, size: 2048 MB, Compressed Oops mode: 32-bit

CDS archive(s) mapped at: [0x000001d450000000-0x000001d450c90000-0x000001d450c90000), size 13172736, SharedBaseAddress: 0x000001d450000000, ArchiveRelocationMode: 1.
Compressed class space mapped at: 0x000001d451000000-0x000001d491000000, reserved size: 1073741824
Narrow klass base: 0x000001d450000000, Narrow klass shift: 0, Narrow klass range: 0x100000000

GC Precious Log:
 CardTable entry size: 512
 Card Set container configuration: InlinePtr #cards 5 size 8 Array Of Cards #cards 12 size 40 Howl #buckets 4 coarsen threshold 1843 Howl Bitmap #cards 512 size 80 coarsen threshold 460 Card regions per heap region 1 cards per card region 2048
 CPUs: 8 total, 8 available
 Memory: 22476M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (32-bit)
 Heap Region Size: 1M
 Heap Min Capacity: 8M
 Heap Initial Capacity: 352M
 Heap Max Capacity: 2G
 Pre-touch: Disabled
 Parallel Workers: 8
 Concurrent Workers: 2
 Concurrent Refinement Workers: 8
 Periodic GC: Disabled

Heap:
 garbage-first heap   total 304128K, used 203104K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 12 young (12288K), 12 survivors (12288K)
 Metaspace       used 110442K, committed 112768K, reserved 1179648K
  class space    used 15443K, committed 16576K, reserved 1048576K

Heap Regions: E=young(eden), S=young(survivor), O=old, HS=humongous(starts), HC=humongous(continues), CS=collection set, F=free, TAMS=top-at-mark-start, PB=parsable bottom
|   0|0x0000000080000000, 0x0000000080100000, 0x0000000080100000|100%|HS|  |TAMS 0x0000000080100000| PB 0x0000000080000000| Complete 
|   1|0x0000000080100000, 0x0000000080200000, 0x0000000080200000|100%|HC|  |TAMS 0x0000000080200000| PB 0x0000000080100000| Complete 
|   2|0x0000000080200000, 0x0000000080300000, 0x0000000080300000|100%|HC|  |TAMS 0x0000000080300000| PB 0x0000000080200000| Complete 
|   3|0x0000000080300000, 0x0000000080400000, 0x0000000080400000|100%| O|  |TAMS 0x0000000080400000| PB 0x0000000080300000| Untracked 
|   4|0x0000000080400000, 0x0000000080500000, 0x0000000080500000|100%| O|  |TAMS 0x0000000080500000| PB 0x0000000080400000| Untracked 
|   5|0x0000000080500000, 0x0000000080600000, 0x0000000080600000|100%| O|  |TAMS 0x0000000080600000| PB 0x0000000080500000| Untracked 
|   6|0x0000000080600000, 0x0000000080700000, 0x0000000080700000|100%|HS|  |TAMS 0x0000000080700000| PB 0x0000000080600000| Complete 
|   7|0x0000000080700000, 0x0000000080800000, 0x0000000080800000|100%| O|  |TAMS 0x0000000080800000| PB 0x0000000080700000| Untracked 
|   8|0x0000000080800000, 0x0000000080900000, 0x0000000080900000|100%| O|  |TAMS 0x0000000080900000| PB 0x0000000080800000| Untracked 
|   9|0x0000000080900000, 0x0000000080a00000, 0x0000000080a00000|100%| O|  |TAMS 0x0000000080a00000| PB 0x0000000080900000| Untracked 
|  10|0x0000000080a00000, 0x0000000080b00000, 0x0000000080b00000|100%| O|  |TAMS 0x0000000080b00000| PB 0x0000000080a00000| Untracked 
|  11|0x0000000080b00000, 0x0000000080c00000, 0x0000000080c00000|100%| O|  |TAMS 0x0000000080c00000| PB 0x0000000080b00000| Untracked 
|  12|0x0000000080c00000, 0x0000000080d00000, 0x0000000080d00000|100%| O|  |TAMS 0x0000000080d00000| PB 0x0000000080c00000| Untracked 
|  13|0x0000000080d00000, 0x0000000080e00000, 0x0000000080e00000|100%| O|  |TAMS 0x0000000080e00000| PB 0x0000000080d00000| Untracked 
|  14|0x0000000080e00000, 0x0000000080f00000, 0x0000000080f00000|100%| O|  |TAMS 0x0000000080f00000| PB 0x0000000080e00000| Untracked 
|  15|0x0000000080f00000, 0x0000000081000000, 0x0000000081000000|100%| O|  |TAMS 0x0000000081000000| PB 0x0000000080f00000| Untracked 
|  16|0x0000000081000000, 0x0000000081100000, 0x0000000081100000|100%| O|  |TAMS 0x0000000081100000| PB 0x0000000081000000| Untracked 
|  17|0x0000000081100000, 0x0000000081200000, 0x0000000081200000|100%| O|  |TAMS 0x0000000081200000| PB 0x0000000081100000| Untracked 
|  18|0x0000000081200000, 0x0000000081300000, 0x0000000081300000|100%| O|  |TAMS 0x0000000081300000| PB 0x0000000081200000| Untracked 
|  19|0x0000000081300000, 0x0000000081400000, 0x0000000081400000|100%| O|  |TAMS 0x0000000081400000| PB 0x0000000081300000| Untracked 
|  20|0x0000000081400000, 0x0000000081500000, 0x0000000081500000|100%| O|  |TAMS 0x0000000081500000| PB 0x0000000081400000| Untracked 
|  21|0x0000000081500000, 0x0000000081600000, 0x0000000081600000|100%| O|  |TAMS 0x0000000081600000| PB 0x0000000081500000| Untracked 
|  22|0x0000000081600000, 0x0000000081700000, 0x0000000081700000|100%| O|  |TAMS 0x0000000081700000| PB 0x0000000081600000| Untracked 
|  23|0x0000000081700000, 0x0000000081800000, 0x0000000081800000|100%| O|  |TAMS 0x0000000081800000| PB 0x0000000081700000| Untracked 
|  24|0x0000000081800000, 0x0000000081900000, 0x0000000081900000|100%| O|  |TAMS 0x0000000081900000| PB 0x0000000081800000| Untracked 
|  25|0x0000000081900000, 0x0000000081a00000, 0x0000000081a00000|100%| O|  |TAMS 0x0000000081a00000| PB 0x0000000081900000| Untracked 
|  26|0x0000000081a00000, 0x0000000081b00000, 0x0000000081b00000|100%| O|  |TAMS 0x0000000081b00000| PB 0x0000000081a00000| Untracked 
|  27|0x0000000081b00000, 0x0000000081c00000, 0x0000000081c00000|100%| O|  |TAMS 0x0000000081c00000| PB 0x0000000081b00000| Untracked 
|  28|0x0000000081c00000, 0x0000000081d00000, 0x0000000081d00000|100%| O|  |TAMS 0x0000000081d00000| PB 0x0000000081c00000| Untracked 
|  29|0x0000000081d00000, 0x0000000081e00000, 0x0000000081e00000|100%| O|  |TAMS 0x0000000081e00000| PB 0x0000000081d00000| Untracked 
|  30|0x0000000081e00000, 0x0000000081f00000, 0x0000000081f00000|100%| O|  |TAMS 0x0000000081f00000| PB 0x0000000081e00000| Untracked 
|  31|0x0000000081f00000, 0x0000000082000000, 0x0000000082000000|100%| O|  |TAMS 0x0000000082000000| PB 0x0000000081f00000| Untracked 
|  32|0x0000000082000000, 0x0000000082100000, 0x0000000082100000|100%| O|  |TAMS 0x0000000082100000| PB 0x0000000082000000| Untracked 
|  33|0x0000000082100000, 0x0000000082200000, 0x0000000082200000|100%| O|  |TAMS 0x0000000082200000| PB 0x0000000082100000| Untracked 
|  34|0x0000000082200000, 0x0000000082300000, 0x0000000082300000|100%| O|  |TAMS 0x0000000082300000| PB 0x0000000082200000| Untracked 
|  35|0x0000000082300000, 0x0000000082400000, 0x0000000082400000|100%| O|  |TAMS 0x0000000082400000| PB 0x0000000082300000| Untracked 
|  36|0x0000000082400000, 0x0000000082500000, 0x0000000082500000|100%| O|  |TAMS 0x0000000082500000| PB 0x0000000082400000| Untracked 
|  37|0x0000000082500000, 0x0000000082600000, 0x0000000082600000|100%| O|  |TAMS 0x0000000082600000| PB 0x0000000082500000| Untracked 
|  38|0x0000000082600000, 0x0000000082700000, 0x0000000082700000|100%| O|  |TAMS 0x0000000082700000| PB 0x0000000082600000| Untracked 
|  39|0x0000000082700000, 0x0000000082800000, 0x0000000082800000|100%|HS|  |TAMS 0x0000000082800000| PB 0x0000000082700000| Complete 
|  40|0x0000000082800000, 0x0000000082900000, 0x0000000082900000|100%|HS|  |TAMS 0x0000000082900000| PB 0x0000000082800000| Complete 
|  41|0x0000000082900000, 0x0000000082a00000, 0x0000000082a00000|100%| O|  |TAMS 0x0000000082a00000| PB 0x0000000082900000| Untracked 
|  42|0x0000000082a00000, 0x0000000082b00000, 0x0000000082b00000|100%| O|  |TAMS 0x0000000082b00000| PB 0x0000000082a00000| Untracked 
|  43|0x0000000082b00000, 0x0000000082c00000, 0x0000000082c00000|100%| O|  |TAMS 0x0000000082c00000| PB 0x0000000082b00000| Untracked 
|  44|0x0000000082c00000, 0x0000000082d00000, 0x0000000082d00000|100%| O|  |TAMS 0x0000000082d00000| PB 0x0000000082c00000| Untracked 
|  45|0x0000000082d00000, 0x0000000082e00000, 0x0000000082e00000|100%| O|  |TAMS 0x0000000082e00000| PB 0x0000000082d00000| Untracked 
|  46|0x0000000082e00000, 0x0000000082f00000, 0x0000000082f00000|100%| O|  |TAMS 0x0000000082f00000| PB 0x0000000082e00000| Untracked 
|  47|0x0000000082f00000, 0x0000000083000000, 0x0000000083000000|100%| O|  |TAMS 0x0000000083000000| PB 0x0000000082f00000| Untracked 
|  48|0x0000000083000000, 0x0000000083100000, 0x0000000083100000|100%| O|  |TAMS 0x0000000083100000| PB 0x0000000083000000| Untracked 
|  49|0x0000000083100000, 0x0000000083200000, 0x0000000083200000|100%|HS|  |TAMS 0x0000000083200000| PB 0x0000000083100000| Complete 
|  50|0x0000000083200000, 0x0000000083300000, 0x0000000083300000|100%|HS|  |TAMS 0x0000000083300000| PB 0x0000000083200000| Complete 
|  51|0x0000000083300000, 0x0000000083400000, 0x0000000083400000|100%| O|  |TAMS 0x0000000083400000| PB 0x0000000083300000| Untracked 
|  52|0x0000000083400000, 0x0000000083500000, 0x0000000083500000|100%| O|  |TAMS 0x0000000083500000| PB 0x0000000083400000| Untracked 
|  53|0x0000000083500000, 0x0000000083600000, 0x0000000083600000|100%|HS|  |TAMS 0x0000000083600000| PB 0x0000000083500000| Complete 
|  54|0x0000000083600000, 0x0000000083700000, 0x0000000083700000|100%|HS|  |TAMS 0x0000000083700000| PB 0x0000000083600000| Complete 
|  55|0x0000000083700000, 0x0000000083800000, 0x0000000083800000|100%| O|  |TAMS 0x0000000083800000| PB 0x0000000083700000| Untracked 
|  56|0x0000000083800000, 0x0000000083900000, 0x0000000083900000|100%| O|  |TAMS 0x0000000083900000| PB 0x0000000083800000| Untracked 
|  57|0x0000000083900000, 0x0000000083a00000, 0x0000000083a00000|100%| O|  |TAMS 0x0000000083a00000| PB 0x0000000083900000| Untracked 
|  58|0x0000000083a00000, 0x0000000083b00000, 0x0000000083b00000|100%| O|  |TAMS 0x0000000083b00000| PB 0x0000000083a00000| Untracked 
|  59|0x0000000083b00000, 0x0000000083c00000, 0x0000000083c00000|100%| O|  |TAMS 0x0000000083c00000| PB 0x0000000083b00000| Untracked 
|  60|0x0000000083c00000, 0x0000000083d00000, 0x0000000083d00000|100%| O|  |TAMS 0x0000000083d00000| PB 0x0000000083c00000| Untracked 
|  61|0x0000000083d00000, 0x0000000083e00000, 0x0000000083e00000|100%| O|  |TAMS 0x0000000083e00000| PB 0x0000000083d00000| Untracked 
|  62|0x0000000083e00000, 0x0000000083f00000, 0x0000000083f00000|100%| O|  |TAMS 0x0000000083f00000| PB 0x0000000083e00000| Untracked 
|  63|0x0000000083f00000, 0x0000000084000000, 0x0000000084000000|100%| O|  |TAMS 0x0000000084000000| PB 0x0000000083f00000| Untracked 
|  64|0x0000000084000000, 0x0000000084100000, 0x0000000084100000|100%| O|  |TAMS 0x0000000084100000| PB 0x0000000084000000| Untracked 
|  65|0x0000000084100000, 0x0000000084200000, 0x0000000084200000|100%|HS|  |TAMS 0x0000000084200000| PB 0x0000000084100000| Complete 
|  66|0x0000000084200000, 0x0000000084300000, 0x0000000084300000|100%| O|  |TAMS 0x0000000084300000| PB 0x0000000084200000| Untracked 
|  67|0x0000000084300000, 0x0000000084400000, 0x0000000084400000|100%| O|  |TAMS 0x0000000084300000| PB 0x0000000084300000| Untracked 
|  68|0x0000000084400000, 0x0000000084500000, 0x0000000084500000|100%| O|  |TAMS 0x0000000084500000| PB 0x0000000084400000| Untracked 
|  69|0x0000000084500000, 0x0000000084600000, 0x0000000084600000|100%| O|  |TAMS 0x0000000084600000| PB 0x0000000084500000| Untracked 
|  70|0x0000000084600000, 0x0000000084700000, 0x0000000084700000|100%| O|  |TAMS 0x0000000084700000| PB 0x0000000084600000| Untracked 
|  71|0x0000000084700000, 0x0000000084800000, 0x0000000084800000|100%| O|  |TAMS 0x0000000084800000| PB 0x0000000084700000| Untracked 
|  72|0x0000000084800000, 0x0000000084900000, 0x0000000084900000|100%| O|  |TAMS 0x0000000084900000| PB 0x0000000084800000| Untracked 
|  73|0x0000000084900000, 0x0000000084a00000, 0x0000000084a00000|100%| O|  |TAMS 0x0000000084a00000| PB 0x0000000084900000| Untracked 
|  74|0x0000000084a00000, 0x0000000084b00000, 0x0000000084b00000|100%| O|  |TAMS 0x0000000084b00000| PB 0x0000000084a00000| Untracked 
|  75|0x0000000084b00000, 0x0000000084c00000, 0x0000000084c00000|100%| O|  |TAMS 0x0000000084c00000| PB 0x0000000084b00000| Untracked 
|  76|0x0000000084c00000, 0x0000000084d00000, 0x0000000084d00000|100%| O|  |TAMS 0x0000000084d00000| PB 0x0000000084c00000| Untracked 
|  77|0x0000000084d00000, 0x0000000084e00000, 0x0000000084e00000|100%| O|  |TAMS 0x0000000084e00000| PB 0x0000000084d00000| Untracked 
|  78|0x0000000084e00000, 0x0000000084f00000, 0x0000000084f00000|100%| O|  |TAMS 0x0000000084f00000| PB 0x0000000084e00000| Untracked 
|  79|0x0000000084f00000, 0x0000000085000000, 0x0000000085000000|100%| O|  |TAMS 0x0000000085000000| PB 0x0000000084f00000| Untracked 
|  80|0x0000000085000000, 0x0000000085100000, 0x0000000085100000|100%| O|  |TAMS 0x0000000085100000| PB 0x0000000085000000| Untracked 
|  81|0x0000000085100000, 0x0000000085200000, 0x0000000085200000|100%| O|  |TAMS 0x0000000085200000| PB 0x0000000085100000| Untracked 
|  82|0x0000000085200000, 0x0000000085300000, 0x0000000085300000|100%| O|  |TAMS 0x0000000085300000| PB 0x0000000085200000| Untracked 
|  83|0x0000000085300000, 0x0000000085400000, 0x0000000085400000|100%| O|  |TAMS 0x0000000085400000| PB 0x0000000085300000| Untracked 
|  84|0x0000000085400000, 0x0000000085500000, 0x0000000085500000|100%| O|  |TAMS 0x0000000085500000| PB 0x0000000085400000| Untracked 
|  85|0x0000000085500000, 0x0000000085600000, 0x0000000085600000|100%| O|  |TAMS 0x0000000085600000| PB 0x0000000085500000| Untracked 
|  86|0x0000000085600000, 0x0000000085700000, 0x0000000085700000|100%| O|  |TAMS 0x0000000085700000| PB 0x0000000085600000| Untracked 
|  87|0x0000000085700000, 0x0000000085800000, 0x0000000085800000|100%| O|  |TAMS 0x0000000085800000| PB 0x0000000085700000| Untracked 
|  88|0x0000000085800000, 0x0000000085900000, 0x0000000085900000|100%| O|  |TAMS 0x0000000085900000| PB 0x0000000085800000| Untracked 
|  89|0x0000000085900000, 0x0000000085a00000, 0x0000000085a00000|100%| O|  |TAMS 0x0000000085a00000| PB 0x0000000085900000| Untracked 
|  90|0x0000000085a00000, 0x0000000085b00000, 0x0000000085b00000|100%| O|  |TAMS 0x0000000085b00000| PB 0x0000000085a00000| Untracked 
|  91|0x0000000085b00000, 0x0000000085c00000, 0x0000000085c00000|100%| O|  |TAMS 0x0000000085c00000| PB 0x0000000085b00000| Untracked 
|  92|0x0000000085c00000, 0x0000000085d00000, 0x0000000085d00000|100%| O|  |TAMS 0x0000000085d00000| PB 0x0000000085c00000| Untracked 
|  93|0x0000000085d00000, 0x0000000085e00000, 0x0000000085e00000|100%| O|  |TAMS 0x0000000085e00000| PB 0x0000000085d00000| Untracked 
|  94|0x0000000085e00000, 0x0000000085f00000, 0x0000000085f00000|100%| O|  |TAMS 0x0000000085f00000| PB 0x0000000085e00000| Untracked 
|  95|0x0000000085f00000, 0x0000000086000000, 0x0000000086000000|100%| O|  |TAMS 0x0000000086000000| PB 0x0000000085f00000| Untracked 
|  96|0x0000000086000000, 0x0000000086100000, 0x0000000086100000|100%| O|  |TAMS 0x0000000086100000| PB 0x0000000086000000| Untracked 
|  97|0x0000000086100000, 0x0000000086200000, 0x0000000086200000|100%| O|  |TAMS 0x0000000086200000| PB 0x0000000086100000| Untracked 
|  98|0x0000000086200000, 0x0000000086300000, 0x0000000086300000|100%| O|  |TAMS 0x0000000086300000| PB 0x0000000086200000| Untracked 
|  99|0x0000000086300000, 0x0000000086400000, 0x0000000086400000|100%| O|  |TAMS 0x0000000086400000| PB 0x0000000086300000| Untracked 
| 100|0x0000000086400000, 0x0000000086500000, 0x0000000086500000|100%| O|  |TAMS 0x0000000086500000| PB 0x0000000086400000| Untracked 
| 101|0x0000000086500000, 0x0000000086600000, 0x0000000086600000|100%| O|  |TAMS 0x0000000086600000| PB 0x0000000086500000| Untracked 
| 102|0x0000000086600000, 0x0000000086700000, 0x0000000086700000|100%| O|  |TAMS 0x0000000086700000| PB 0x0000000086600000| Untracked 
| 103|0x0000000086700000, 0x0000000086800000, 0x0000000086800000|100%| O|  |TAMS 0x0000000086800000| PB 0x0000000086700000| Untracked 
| 104|0x0000000086800000, 0x0000000086900000, 0x0000000086900000|100%| O|  |TAMS 0x0000000086900000| PB 0x0000000086800000| Untracked 
| 105|0x0000000086900000, 0x0000000086a00000, 0x0000000086a00000|100%| O|  |TAMS 0x0000000086900000| PB 0x0000000086900000| Untracked 
| 106|0x0000000086a00000, 0x0000000086b00000, 0x0000000086b00000|100%| O|  |TAMS 0x0000000086b00000| PB 0x0000000086a00000| Untracked 
| 107|0x0000000086b00000, 0x0000000086c00000, 0x0000000086c00000|100%| O|  |TAMS 0x0000000086c00000| PB 0x0000000086b00000| Untracked 
| 108|0x0000000086c00000, 0x0000000086d00000, 0x0000000086d00000|100%| O|  |TAMS 0x0000000086d00000| PB 0x0000000086c00000| Untracked 
| 109|0x0000000086d00000, 0x0000000086e00000, 0x0000000086e00000|100%|HS|  |TAMS 0x0000000086e00000| PB 0x0000000086d00000| Complete 
| 110|0x0000000086e00000, 0x0000000086f00000, 0x0000000086f00000|100%|HC|  |TAMS 0x0000000086f00000| PB 0x0000000086e00000| Complete 
| 111|0x0000000086f00000, 0x0000000087000000, 0x0000000087000000|100%| O|  |TAMS 0x0000000087000000| PB 0x0000000086f00000| Untracked 
| 112|0x0000000087000000, 0x0000000087100000, 0x0000000087100000|100%| O|  |TAMS 0x0000000087100000| PB 0x0000000087000000| Untracked 
| 113|0x0000000087100000, 0x0000000087200000, 0x0000000087200000|100%| O|  |TAMS 0x0000000087200000| PB 0x0000000087100000| Untracked 
| 114|0x0000000087200000, 0x0000000087300000, 0x0000000087300000|100%| O|  |TAMS 0x0000000087300000| PB 0x0000000087200000| Untracked 
| 115|0x0000000087300000, 0x0000000087400000, 0x0000000087400000|100%| O|  |TAMS 0x0000000087400000| PB 0x0000000087300000| Untracked 
| 116|0x0000000087400000, 0x0000000087500000, 0x0000000087500000|100%| O|  |TAMS 0x0000000087500000| PB 0x0000000087400000| Untracked 
| 117|0x0000000087500000, 0x0000000087600000, 0x0000000087600000|100%| O|  |TAMS 0x0000000087600000| PB 0x0000000087500000| Untracked 
| 118|0x0000000087600000, 0x0000000087700000, 0x0000000087700000|100%| O|  |TAMS 0x0000000087600000| PB 0x0000000087600000| Untracked 
| 119|0x0000000087700000, 0x0000000087800000, 0x0000000087800000|100%| O|  |TAMS 0x0000000087800000| PB 0x0000000087700000| Untracked 
| 120|0x0000000087800000, 0x0000000087900000, 0x0000000087900000|100%| O|  |TAMS 0x0000000087900000| PB 0x0000000087800000| Untracked 
| 121|0x0000000087900000, 0x0000000087a00000, 0x0000000087a00000|100%| O|  |TAMS 0x0000000087a00000| PB 0x0000000087900000| Untracked 
| 122|0x0000000087a00000, 0x0000000087b00000, 0x0000000087b00000|100%| O|  |TAMS 0x0000000087b00000| PB 0x0000000087a00000| Untracked 
| 123|0x0000000087b00000, 0x0000000087c00000, 0x0000000087c00000|100%| O|  |TAMS 0x0000000087c00000| PB 0x0000000087b00000| Untracked 
| 124|0x0000000087c00000, 0x0000000087d00000, 0x0000000087d00000|100%| O|  |TAMS 0x0000000087d00000| PB 0x0000000087c00000| Untracked 
| 125|0x0000000087d00000, 0x0000000087e00000, 0x0000000087e00000|100%| O|  |TAMS 0x0000000087e00000| PB 0x0000000087d00000| Untracked 
| 126|0x0000000087e00000, 0x0000000087f00000, 0x0000000087f00000|100%| O|  |TAMS 0x0000000087f00000| PB 0x0000000087e00000| Untracked 
| 127|0x0000000087f00000, 0x0000000088000000, 0x0000000088000000|100%| O|  |TAMS 0x0000000088000000| PB 0x0000000087f00000| Untracked 
| 128|0x0000000088000000, 0x0000000088100000, 0x0000000088100000|100%| O|  |TAMS 0x0000000088100000| PB 0x0000000088000000| Untracked 
| 129|0x0000000088100000, 0x0000000088200000, 0x0000000088200000|100%| O|  |TAMS 0x0000000088200000| PB 0x0000000088100000| Untracked 
| 130|0x0000000088200000, 0x0000000088300000, 0x0000000088300000|100%| O|  |TAMS 0x0000000088300000| PB 0x0000000088200000| Untracked 
| 131|0x0000000088300000, 0x0000000088400000, 0x0000000088400000|100%| O|  |TAMS 0x0000000088300000| PB 0x0000000088300000| Untracked 
| 132|0x0000000088400000, 0x0000000088500000, 0x0000000088500000|100%| O|  |TAMS 0x0000000088400000| PB 0x0000000088400000| Untracked 
| 133|0x0000000088500000, 0x0000000088600000, 0x0000000088600000|100%| O|  |TAMS 0x0000000088600000| PB 0x0000000088500000| Untracked 
| 134|0x0000000088600000, 0x0000000088700000, 0x0000000088700000|100%| O|  |TAMS 0x0000000088700000| PB 0x0000000088600000| Untracked 
| 135|0x0000000088700000, 0x0000000088800000, 0x0000000088800000|100%| O|  |TAMS 0x0000000088800000| PB 0x0000000088700000| Untracked 
| 136|0x0000000088800000, 0x0000000088900000, 0x0000000088900000|100%| O|  |TAMS 0x0000000088900000| PB 0x0000000088800000| Untracked 
| 137|0x0000000088900000, 0x0000000088a00000, 0x0000000088a00000|100%| O|  |TAMS 0x0000000088a00000| PB 0x0000000088900000| Untracked 
| 138|0x0000000088a00000, 0x0000000088b00000, 0x0000000088b00000|100%| O|  |TAMS 0x0000000088b00000| PB 0x0000000088a00000| Untracked 
| 139|0x0000000088b00000, 0x0000000088c00000, 0x0000000088c00000|100%| O|  |TAMS 0x0000000088c00000| PB 0x0000000088b00000| Untracked 
| 140|0x0000000088c00000, 0x0000000088d00000, 0x0000000088d00000|100%| O|  |TAMS 0x0000000088d00000| PB 0x0000000088c00000| Untracked 
| 141|0x0000000088d00000, 0x0000000088e00000, 0x0000000088e00000|100%| O|  |TAMS 0x0000000088e00000| PB 0x0000000088d00000| Untracked 
| 142|0x0000000088e00000, 0x0000000088f00000, 0x0000000088f00000|100%| O|  |TAMS 0x0000000088f00000| PB 0x0000000088e00000| Untracked 
| 143|0x0000000088f00000, 0x0000000089000000, 0x0000000089000000|100%| O|  |TAMS 0x0000000089000000| PB 0x0000000088f00000| Untracked 
| 144|0x0000000089000000, 0x0000000089100000, 0x0000000089100000|100%| O|  |TAMS 0x0000000089100000| PB 0x0000000089000000| Untracked 
| 145|0x0000000089100000, 0x0000000089200000, 0x0000000089200000|100%| O|  |TAMS 0x0000000089200000| PB 0x0000000089100000| Untracked 
| 146|0x0000000089200000, 0x0000000089300000, 0x0000000089300000|100%| O|  |TAMS 0x0000000089300000| PB 0x0000000089200000| Untracked 
| 147|0x0000000089300000, 0x0000000089400000, 0x0000000089400000|100%| O|  |TAMS 0x0000000089400000| PB 0x0000000089300000| Untracked 
| 148|0x0000000089400000, 0x0000000089500000, 0x0000000089500000|100%| O|  |TAMS 0x0000000089500000| PB 0x0000000089400000| Untracked 
| 149|0x0000000089500000, 0x0000000089600000, 0x0000000089600000|100%| O|  |TAMS 0x0000000089600000| PB 0x0000000089500000| Untracked 
| 150|0x0000000089600000, 0x0000000089700000, 0x0000000089700000|100%| O|  |TAMS 0x0000000089700000| PB 0x0000000089600000| Untracked 
| 151|0x0000000089700000, 0x0000000089800000, 0x0000000089800000|100%| O|  |TAMS 0x0000000089800000| PB 0x0000000089700000| Untracked 
| 152|0x0000000089800000, 0x0000000089900000, 0x0000000089900000|100%| O|  |TAMS 0x0000000089900000| PB 0x0000000089800000| Untracked 
| 153|0x0000000089900000, 0x0000000089a00000, 0x0000000089a00000|100%| O|  |TAMS 0x0000000089a00000| PB 0x0000000089900000| Untracked 
| 154|0x0000000089a00000, 0x0000000089b00000, 0x0000000089b00000|100%| O|  |TAMS 0x0000000089b00000| PB 0x0000000089a00000| Untracked 
| 155|0x0000000089b00000, 0x0000000089c00000, 0x0000000089c00000|100%| O|  |TAMS 0x0000000089c00000| PB 0x0000000089b00000| Untracked 
| 156|0x0000000089c00000, 0x0000000089d00000, 0x0000000089d00000|100%| O|  |TAMS 0x0000000089d00000| PB 0x0000000089c00000| Untracked 
| 157|0x0000000089d00000, 0x0000000089e00000, 0x0000000089e00000|100%| O|  |TAMS 0x0000000089e00000| PB 0x0000000089d00000| Untracked 
| 158|0x0000000089e00000, 0x0000000089f00000, 0x0000000089f00000|100%| O|  |TAMS 0x0000000089f00000| PB 0x0000000089e00000| Untracked 
| 159|0x0000000089f00000, 0x000000008a000000, 0x000000008a000000|100%| O|  |TAMS 0x000000008a000000| PB 0x0000000089f00000| Untracked 
| 160|0x000000008a000000, 0x000000008a100000, 0x000000008a100000|100%| O|  |TAMS 0x000000008a100000| PB 0x000000008a000000| Untracked 
| 161|0x000000008a100000, 0x000000008a200000, 0x000000008a200000|100%| O|  |TAMS 0x000000008a200000| PB 0x000000008a100000| Untracked 
| 162|0x000000008a200000, 0x000000008a300000, 0x000000008a300000|100%| O|  |TAMS 0x000000008a300000| PB 0x000000008a200000| Untracked 
| 163|0x000000008a300000, 0x000000008a400000, 0x000000008a400000|100%| O|  |TAMS 0x000000008a400000| PB 0x000000008a300000| Untracked 
| 164|0x000000008a400000, 0x000000008a500000, 0x000000008a500000|100%| O|  |TAMS 0x000000008a500000| PB 0x000000008a400000| Untracked 
| 165|0x000000008a500000, 0x000000008a600000, 0x000000008a600000|100%| O|  |TAMS 0x000000008a600000| PB 0x000000008a500000| Untracked 
| 166|0x000000008a600000, 0x000000008a700000, 0x000000008a700000|100%| O|  |TAMS 0x000000008a700000| PB 0x000000008a600000| Untracked 
| 167|0x000000008a700000, 0x000000008a800000, 0x000000008a800000|100%| O|  |TAMS 0x000000008a800000| PB 0x000000008a700000| Untracked 
| 168|0x000000008a800000, 0x000000008a900000, 0x000000008a900000|100%| O|  |TAMS 0x000000008a900000| PB 0x000000008a800000| Untracked 
| 169|0x000000008a900000, 0x000000008aa00000, 0x000000008aa00000|100%| O|  |TAMS 0x000000008aa00000| PB 0x000000008a900000| Untracked 
| 170|0x000000008aa00000, 0x000000008ab00000, 0x000000008ab00000|100%| O|  |TAMS 0x000000008aa00000| PB 0x000000008aa00000| Untracked 
| 171|0x000000008ab00000, 0x000000008ac00000, 0x000000008ac00000|100%| O|  |TAMS 0x000000008ab00000| PB 0x000000008ab00000| Untracked 
| 172|0x000000008ac00000, 0x000000008ad00000, 0x000000008ad00000|100%| O|  |TAMS 0x000000008ac00000| PB 0x000000008ac00000| Untracked 
| 173|0x000000008ad00000, 0x000000008ae00000, 0x000000008ae00000|100%| O|  |TAMS 0x000000008ad00000| PB 0x000000008ad00000| Untracked 
| 174|0x000000008ae00000, 0x000000008af00000, 0x000000008af00000|100%| O|  |TAMS 0x000000008ae00000| PB 0x000000008ae00000| Untracked 
| 175|0x000000008af00000, 0x000000008b000000, 0x000000008b000000|100%| O|  |TAMS 0x000000008af00000| PB 0x000000008af00000| Untracked 
| 176|0x000000008b000000, 0x000000008b100000, 0x000000008b100000|100%| O|  |TAMS 0x000000008b000000| PB 0x000000008b000000| Untracked 
| 177|0x000000008b100000, 0x000000008b200000, 0x000000008b200000|100%| O|  |TAMS 0x000000008b100000| PB 0x000000008b100000| Untracked 
| 178|0x000000008b200000, 0x000000008b300000, 0x000000008b300000|100%| O|  |TAMS 0x000000008b200000| PB 0x000000008b200000| Untracked 
| 179|0x000000008b300000, 0x000000008b300000, 0x000000008b400000|  0%| F|  |TAMS 0x000000008b300000| PB 0x000000008b300000| Untracked 
| 180|0x000000008b400000, 0x000000008b400000, 0x000000008b500000|  0%| F|  |TAMS 0x000000008b400000| PB 0x000000008b400000| Untracked 
| 181|0x000000008b500000, 0x000000008b500000, 0x000000008b600000|  0%| F|  |TAMS 0x000000008b500000| PB 0x000000008b500000| Untracked 
| 182|0x000000008b600000, 0x000000008b600000, 0x000000008b700000|  0%| F|  |TAMS 0x000000008b600000| PB 0x000000008b600000| Untracked 
| 183|0x000000008b700000, 0x000000008b700000, 0x000000008b800000|  0%| F|  |TAMS 0x000000008b700000| PB 0x000000008b700000| Untracked 
| 184|0x000000008b800000, 0x000000008b800000, 0x000000008b900000|  0%| F|  |TAMS 0x000000008b800000| PB 0x000000008b800000| Untracked 
| 185|0x000000008b900000, 0x000000008b900000, 0x000000008ba00000|  0%| F|  |TAMS 0x000000008b900000| PB 0x000000008b900000| Untracked 
| 186|0x000000008ba00000, 0x000000008ba00000, 0x000000008bb00000|  0%| F|  |TAMS 0x000000008ba00000| PB 0x000000008ba00000| Untracked 
| 187|0x000000008bb00000, 0x000000008bb00000, 0x000000008bc00000|  0%| F|  |TAMS 0x000000008bb00000| PB 0x000000008bb00000| Untracked 
| 188|0x000000008bc00000, 0x000000008bc00000, 0x000000008bd00000|  0%| F|  |TAMS 0x000000008bc00000| PB 0x000000008bc00000| Untracked 
| 189|0x000000008bd00000, 0x000000008bd00000, 0x000000008be00000|  0%| F|  |TAMS 0x000000008bd00000| PB 0x000000008bd00000| Untracked 
| 190|0x000000008be00000, 0x000000008be00000, 0x000000008bf00000|  0%| F|  |TAMS 0x000000008be00000| PB 0x000000008be00000| Untracked 
| 191|0x000000008bf00000, 0x000000008bf00000, 0x000000008c000000|  0%| F|  |TAMS 0x000000008bf00000| PB 0x000000008bf00000| Untracked 
| 192|0x000000008c000000, 0x000000008c100000, 0x000000008c100000|100%| O|  |TAMS 0x000000008c000000| PB 0x000000008c000000| Untracked 
| 193|0x000000008c100000, 0x000000008c200000, 0x000000008c200000|100%| O|  |TAMS 0x000000008c100000| PB 0x000000008c100000| Untracked 
| 194|0x000000008c200000, 0x000000008c300000, 0x000000008c300000|100%| O|  |TAMS 0x000000008c200000| PB 0x000000008c200000| Untracked 
| 195|0x000000008c300000, 0x000000008c400000, 0x000000008c400000|100%| O|  |TAMS 0x000000008c300000| PB 0x000000008c300000| Untracked 
| 196|0x000000008c400000, 0x000000008c500000, 0x000000008c500000|100%| O|  |TAMS 0x000000008c400000| PB 0x000000008c400000| Untracked 
| 197|0x000000008c500000, 0x000000008c558270, 0x000000008c600000| 34%| O|  |TAMS 0x000000008c500000| PB 0x000000008c500000| Untracked 
| 198|0x000000008c600000, 0x000000008c600000, 0x000000008c700000|  0%| F|  |TAMS 0x000000008c600000| PB 0x000000008c600000| Untracked 
| 199|0x000000008c700000, 0x000000008c700000, 0x000000008c800000|  0%| F|  |TAMS 0x000000008c700000| PB 0x000000008c700000| Untracked 
| 200|0x000000008c800000, 0x000000008c900000, 0x000000008c900000|100%| S|CS|TAMS 0x000000008c800000| PB 0x000000008c800000| Complete 
| 201|0x000000008c900000, 0x000000008ca00000, 0x000000008ca00000|100%| S|CS|TAMS 0x000000008c900000| PB 0x000000008c900000| Complete 
| 202|0x000000008ca00000, 0x000000008cb00000, 0x000000008cb00000|100%| S|CS|TAMS 0x000000008ca00000| PB 0x000000008ca00000| Complete 
| 203|0x000000008cb00000, 0x000000008cc00000, 0x000000008cc00000|100%| S|CS|TAMS 0x000000008cb00000| PB 0x000000008cb00000| Complete 
| 204|0x000000008cc00000, 0x000000008cd00000, 0x000000008cd00000|100%| S|CS|TAMS 0x000000008cc00000| PB 0x000000008cc00000| Complete 
| 205|0x000000008cd00000, 0x000000008ce00000, 0x000000008ce00000|100%| S|CS|TAMS 0x000000008cd00000| PB 0x000000008cd00000| Complete 
| 206|0x000000008ce00000, 0x000000008cf00000, 0x000000008cf00000|100%| S|CS|TAMS 0x000000008ce00000| PB 0x000000008ce00000| Complete 
| 207|0x000000008cf00000, 0x000000008d000000, 0x000000008d000000|100%| S|CS|TAMS 0x000000008cf00000| PB 0x000000008cf00000| Complete 
| 208|0x000000008d000000, 0x000000008d100000, 0x000000008d100000|100%| S|CS|TAMS 0x000000008d000000| PB 0x000000008d000000| Complete 
| 209|0x000000008d100000, 0x000000008d200000, 0x000000008d200000|100%| S|CS|TAMS 0x000000008d100000| PB 0x000000008d100000| Complete 
| 210|0x000000008d200000, 0x000000008d300000, 0x000000008d300000|100%| S|CS|TAMS 0x000000008d200000| PB 0x000000008d200000| Complete 
| 211|0x000000008d300000, 0x000000008d400000, 0x000000008d400000|100%| S|CS|TAMS 0x000000008d300000| PB 0x000000008d300000| Complete 
| 212|0x000000008d400000, 0x000000008d400000, 0x000000008d500000|  0%| F|  |TAMS 0x000000008d400000| PB 0x000000008d400000| Untracked 
| 213|0x000000008d500000, 0x000000008d500000, 0x000000008d600000|  0%| F|  |TAMS 0x000000008d500000| PB 0x000000008d500000| Untracked 
| 214|0x000000008d600000, 0x000000008d600000, 0x000000008d700000|  0%| F|  |TAMS 0x000000008d600000| PB 0x000000008d600000| Untracked 
| 215|0x000000008d700000, 0x000000008d700000, 0x000000008d800000|  0%| F|  |TAMS 0x000000008d700000| PB 0x000000008d700000| Untracked 
| 216|0x000000008d800000, 0x000000008d800000, 0x000000008d900000|  0%| F|  |TAMS 0x000000008d800000| PB 0x000000008d800000| Untracked 
| 217|0x000000008d900000, 0x000000008d900000, 0x000000008da00000|  0%| F|  |TAMS 0x000000008d900000| PB 0x000000008d900000| Untracked 
| 218|0x000000008da00000, 0x000000008da00000, 0x000000008db00000|  0%| F|  |TAMS 0x000000008da00000| PB 0x000000008da00000| Untracked 
| 219|0x000000008db00000, 0x000000008db00000, 0x000000008dc00000|  0%| F|  |TAMS 0x000000008db00000| PB 0x000000008db00000| Untracked 
| 220|0x000000008dc00000, 0x000000008dc00000, 0x000000008dd00000|  0%| F|  |TAMS 0x000000008dc00000| PB 0x000000008dc00000| Untracked 
| 221|0x000000008dd00000, 0x000000008dd00000, 0x000000008de00000|  0%| F|  |TAMS 0x000000008dd00000| PB 0x000000008dd00000| Untracked 
| 222|0x000000008de00000, 0x000000008de00000, 0x000000008df00000|  0%| F|  |TAMS 0x000000008de00000| PB 0x000000008de00000| Untracked 
| 223|0x000000008df00000, 0x000000008df00000, 0x000000008e000000|  0%| F|  |TAMS 0x000000008df00000| PB 0x000000008df00000| Untracked 
| 224|0x000000008e000000, 0x000000008e000000, 0x000000008e100000|  0%| F|  |TAMS 0x000000008e000000| PB 0x000000008e000000| Untracked 
| 225|0x000000008e100000, 0x000000008e100000, 0x000000008e200000|  0%| F|  |TAMS 0x000000008e100000| PB 0x000000008e100000| Untracked 
| 226|0x000000008e200000, 0x000000008e200000, 0x000000008e300000|  0%| F|  |TAMS 0x000000008e200000| PB 0x000000008e200000| Untracked 
| 227|0x000000008e300000, 0x000000008e300000, 0x000000008e400000|  0%| F|  |TAMS 0x000000008e300000| PB 0x000000008e300000| Untracked 
| 228|0x000000008e400000, 0x000000008e400000, 0x000000008e500000|  0%| F|  |TAMS 0x000000008e400000| PB 0x000000008e400000| Untracked 
| 229|0x000000008e500000, 0x000000008e500000, 0x000000008e600000|  0%| F|  |TAMS 0x000000008e500000| PB 0x000000008e500000| Untracked 
| 230|0x000000008e600000, 0x000000008e600000, 0x000000008e700000|  0%| F|  |TAMS 0x000000008e600000| PB 0x000000008e600000| Untracked 
| 231|0x000000008e700000, 0x000000008e700000, 0x000000008e800000|  0%| F|  |TAMS 0x000000008e700000| PB 0x000000008e700000| Untracked 
| 232|0x000000008e800000, 0x000000008e800000, 0x000000008e900000|  0%| F|  |TAMS 0x000000008e800000| PB 0x000000008e800000| Untracked 
| 233|0x000000008e900000, 0x000000008e900000, 0x000000008ea00000|  0%| F|  |TAMS 0x000000008e900000| PB 0x000000008e900000| Untracked 
| 234|0x000000008ea00000, 0x000000008ea00000, 0x000000008eb00000|  0%| F|  |TAMS 0x000000008ea00000| PB 0x000000008ea00000| Untracked 
| 235|0x000000008eb00000, 0x000000008eb00000, 0x000000008ec00000|  0%| F|  |TAMS 0x000000008eb00000| PB 0x000000008eb00000| Untracked 
| 236|0x000000008ec00000, 0x000000008ec00000, 0x000000008ed00000|  0%| F|  |TAMS 0x000000008ec00000| PB 0x000000008ec00000| Untracked 
| 237|0x000000008ed00000, 0x000000008ed00000, 0x000000008ee00000|  0%| F|  |TAMS 0x000000008ed00000| PB 0x000000008ed00000| Untracked 
| 238|0x000000008ee00000, 0x000000008ee00000, 0x000000008ef00000|  0%| F|  |TAMS 0x000000008ee00000| PB 0x000000008ee00000| Untracked 
| 239|0x000000008ef00000, 0x000000008ef00000, 0x000000008f000000|  0%| F|  |TAMS 0x000000008ef00000| PB 0x000000008ef00000| Untracked 
| 240|0x000000008f000000, 0x000000008f000000, 0x000000008f100000|  0%| F|  |TAMS 0x000000008f000000| PB 0x000000008f000000| Untracked 
| 241|0x000000008f100000, 0x000000008f100000, 0x000000008f200000|  0%| F|  |TAMS 0x000000008f100000| PB 0x000000008f100000| Untracked 
| 242|0x000000008f200000, 0x000000008f200000, 0x000000008f300000|  0%| F|  |TAMS 0x000000008f200000| PB 0x000000008f200000| Untracked 
| 243|0x000000008f300000, 0x000000008f300000, 0x000000008f400000|  0%| F|  |TAMS 0x000000008f300000| PB 0x000000008f300000| Untracked 
| 244|0x000000008f400000, 0x000000008f400000, 0x000000008f500000|  0%| F|  |TAMS 0x000000008f400000| PB 0x000000008f400000| Untracked 
| 245|0x000000008f500000, 0x000000008f500000, 0x000000008f600000|  0%| F|  |TAMS 0x000000008f500000| PB 0x000000008f500000| Untracked 
| 246|0x000000008f600000, 0x000000008f600000, 0x000000008f700000|  0%| F|  |TAMS 0x000000008f600000| PB 0x000000008f600000| Untracked 
| 247|0x000000008f700000, 0x000000008f700000, 0x000000008f800000|  0%| F|  |TAMS 0x000000008f700000| PB 0x000000008f700000| Untracked 
| 248|0x000000008f800000, 0x000000008f800000, 0x000000008f900000|  0%| F|  |TAMS 0x000000008f800000| PB 0x000000008f800000| Untracked 
| 249|0x000000008f900000, 0x000000008f900000, 0x000000008fa00000|  0%| F|  |TAMS 0x000000008f900000| PB 0x000000008f900000| Untracked 
| 250|0x000000008fa00000, 0x000000008fa00000, 0x000000008fb00000|  0%| F|  |TAMS 0x000000008fa00000| PB 0x000000008fa00000| Untracked 
| 251|0x000000008fb00000, 0x000000008fb00000, 0x000000008fc00000|  0%| F|  |TAMS 0x000000008fb00000| PB 0x000000008fb00000| Untracked 
| 252|0x000000008fc00000, 0x000000008fc00000, 0x000000008fd00000|  0%| F|  |TAMS 0x000000008fc00000| PB 0x000000008fc00000| Untracked 
| 253|0x000000008fd00000, 0x000000008fd00000, 0x000000008fe00000|  0%| F|  |TAMS 0x000000008fd00000| PB 0x000000008fd00000| Untracked 
| 254|0x000000008fe00000, 0x000000008fe00000, 0x000000008ff00000|  0%| F|  |TAMS 0x000000008fe00000| PB 0x000000008fe00000| Untracked 
| 255|0x000000008ff00000, 0x000000008ff00000, 0x0000000090000000|  0%| F|  |TAMS 0x000000008ff00000| PB 0x000000008ff00000| Untracked 
| 256|0x0000000090000000, 0x0000000090000000, 0x0000000090100000|  0%| F|  |TAMS 0x0000000090000000| PB 0x0000000090000000| Untracked 
| 257|0x0000000090100000, 0x0000000090100000, 0x0000000090200000|  0%| F|  |TAMS 0x0000000090100000| PB 0x0000000090100000| Untracked 
| 258|0x0000000090200000, 0x0000000090200000, 0x0000000090300000|  0%| F|  |TAMS 0x0000000090200000| PB 0x0000000090200000| Untracked 
| 259|0x0000000090300000, 0x0000000090300000, 0x0000000090400000|  0%| F|  |TAMS 0x0000000090300000| PB 0x0000000090300000| Untracked 
| 260|0x0000000090400000, 0x0000000090400000, 0x0000000090500000|  0%| F|  |TAMS 0x0000000090400000| PB 0x0000000090400000| Untracked 
| 261|0x0000000090500000, 0x0000000090500000, 0x0000000090600000|  0%| F|  |TAMS 0x0000000090500000| PB 0x0000000090500000| Untracked 
| 262|0x0000000090600000, 0x0000000090600000, 0x0000000090700000|  0%| F|  |TAMS 0x0000000090600000| PB 0x0000000090600000| Untracked 
| 263|0x0000000090700000, 0x0000000090700000, 0x0000000090800000|  0%| F|  |TAMS 0x0000000090700000| PB 0x0000000090700000| Untracked 
| 264|0x0000000090800000, 0x0000000090800000, 0x0000000090900000|  0%| F|  |TAMS 0x0000000090800000| PB 0x0000000090800000| Untracked 
| 265|0x0000000090900000, 0x0000000090900000, 0x0000000090a00000|  0%| F|  |TAMS 0x0000000090900000| PB 0x0000000090900000| Untracked 
| 266|0x0000000090a00000, 0x0000000090a00000, 0x0000000090b00000|  0%| F|  |TAMS 0x0000000090a00000| PB 0x0000000090a00000| Untracked 
| 267|0x0000000090b00000, 0x0000000090b00000, 0x0000000090c00000|  0%| F|  |TAMS 0x0000000090b00000| PB 0x0000000090b00000| Untracked 
| 268|0x0000000090c00000, 0x0000000090c00000, 0x0000000090d00000|  0%| F|  |TAMS 0x0000000090c00000| PB 0x0000000090c00000| Untracked 
| 269|0x0000000090d00000, 0x0000000090d00000, 0x0000000090e00000|  0%| F|  |TAMS 0x0000000090d00000| PB 0x0000000090d00000| Untracked 
| 270|0x0000000090e00000, 0x0000000090e00000, 0x0000000090f00000|  0%| F|  |TAMS 0x0000000090e00000| PB 0x0000000090e00000| Untracked 
| 271|0x0000000090f00000, 0x0000000090f00000, 0x0000000091000000|  0%| F|  |TAMS 0x0000000090f00000| PB 0x0000000090f00000| Untracked 
| 272|0x0000000091000000, 0x0000000091000000, 0x0000000091100000|  0%| F|  |TAMS 0x0000000091000000| PB 0x0000000091000000| Untracked 
| 273|0x0000000091100000, 0x0000000091100000, 0x0000000091200000|  0%| F|  |TAMS 0x0000000091100000| PB 0x0000000091100000| Untracked 
| 274|0x0000000091200000, 0x0000000091200000, 0x0000000091300000|  0%| F|  |TAMS 0x0000000091200000| PB 0x0000000091200000| Untracked 
| 275|0x0000000091300000, 0x0000000091300000, 0x0000000091400000|  0%| F|  |TAMS 0x0000000091300000| PB 0x0000000091300000| Untracked 
| 276|0x0000000091400000, 0x0000000091400000, 0x0000000091500000|  0%| F|  |TAMS 0x0000000091400000| PB 0x0000000091400000| Untracked 
| 277|0x0000000091500000, 0x0000000091500000, 0x0000000091600000|  0%| F|  |TAMS 0x0000000091500000| PB 0x0000000091500000| Untracked 
| 278|0x0000000091600000, 0x0000000091600000, 0x0000000091700000|  0%| F|  |TAMS 0x0000000091600000| PB 0x0000000091600000| Untracked 
| 279|0x0000000091700000, 0x0000000091700000, 0x0000000091800000|  0%| F|  |TAMS 0x0000000091700000| PB 0x0000000091700000| Untracked 
| 280|0x0000000091800000, 0x0000000091800000, 0x0000000091900000|  0%| F|  |TAMS 0x0000000091800000| PB 0x0000000091800000| Untracked 
| 281|0x0000000091900000, 0x0000000091900000, 0x0000000091a00000|  0%| F|  |TAMS 0x0000000091900000| PB 0x0000000091900000| Untracked 
| 282|0x0000000091a00000, 0x0000000091a00000, 0x0000000091b00000|  0%| F|  |TAMS 0x0000000091a00000| PB 0x0000000091a00000| Untracked 
| 283|0x0000000091b00000, 0x0000000091b00000, 0x0000000091c00000|  0%| F|  |TAMS 0x0000000091b00000| PB 0x0000000091b00000| Untracked 
| 284|0x0000000091c00000, 0x0000000091c00000, 0x0000000091d00000|  0%| F|  |TAMS 0x0000000091c00000| PB 0x0000000091c00000| Untracked 
| 285|0x0000000091d00000, 0x0000000091d00000, 0x0000000091e00000|  0%| F|  |TAMS 0x0000000091d00000| PB 0x0000000091d00000| Untracked 
| 286|0x0000000091e00000, 0x0000000091e00000, 0x0000000091f00000|  0%| F|  |TAMS 0x0000000091e00000| PB 0x0000000091e00000| Untracked 
| 287|0x0000000091f00000, 0x0000000091f00000, 0x0000000092000000|  0%| F|  |TAMS 0x0000000091f00000| PB 0x0000000091f00000| Untracked 
| 288|0x0000000092000000, 0x0000000092000000, 0x0000000092100000|  0%| F|  |TAMS 0x0000000092000000| PB 0x0000000092000000| Untracked 
| 350|0x0000000095e00000, 0x0000000095e00000, 0x0000000095f00000|  0%| F|  |TAMS 0x0000000095e00000| PB 0x0000000095e00000| Untracked 
| 351|0x0000000095f00000, 0x0000000095f00000, 0x0000000096000000|  0%| F|  |TAMS 0x0000000095f00000| PB 0x0000000095f00000| Untracked 
|2042|0x00000000ffa00000, 0x00000000ffa00000, 0x00000000ffb00000|  0%| F|  |TAMS 0x00000000ffa00000| PB 0x00000000ffa00000| Untracked 
|2043|0x00000000ffb00000, 0x00000000ffc00000, 0x00000000ffc00000|100%| O|  |TAMS 0x00000000ffc00000| PB 0x00000000ffb00000| Untracked 
|2044|0x00000000ffc00000, 0x00000000ffd00000, 0x00000000ffd00000|100%| O|  |TAMS 0x00000000ffd00000| PB 0x00000000ffc00000| Untracked 
|2045|0x00000000ffd00000, 0x00000000ffd00000, 0x00000000ffe00000|  0%| F|  |TAMS 0x00000000ffd00000| PB 0x00000000ffd00000| Untracked 
|2046|0x00000000ffe00000, 0x00000000ffe00000, 0x00000000fff00000|  0%| F|  |TAMS 0x00000000ffe00000| PB 0x00000000ffe00000| Untracked 
|2047|0x00000000fff00000, 0x00000000fff00000, 0x0000000100000000|  0%| F|  |TAMS 0x00000000fff00000| PB 0x00000000fff00000| Untracked 

Card table byte_map: [0x000001d44a380000,0x000001d44a780000] _byte_map_base: 0x000001d449f80000

Marking Bits: (CMBitMap*) 0x000001d43283fd40
 Bits: [0x000001d44a780000, 0x000001d44c780000)

Polling page: 0x000001d431fb0000

Metaspace:

Usage:
  Non-class:     92.77 MB used.
      Class:     15.08 MB used.
       Both:    107.85 MB used.

Virtual space:
  Non-class space:      128.00 MB reserved,      93.94 MB ( 73%) committed,  2 nodes.
      Class space:        1.00 GB reserved,      16.19 MB (  2%) committed,  1 nodes.
             Both:        1.12 GB reserved,     110.12 MB ( 10%) committed. 

Chunk freelists:
   Non-Class:  2.08 MB
       Class:  15.83 MB
        Both:  17.91 MB

MaxMetaspaceSize: unlimited
CompressedClassSpaceSize: 1.00 GB
Initial GC threshold: 21.00 MB
Current GC threshold: 175.25 MB
CDS: on
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 8388608.
 - enlarge_chunks_in_place: 1.
 - use_allocation_guard: 0.


Internal statistics:

num_allocs_failed_limit: 6.
num_arena_births: 4150.
num_arena_deaths: 0.
num_vsnodes_births: 3.
num_vsnodes_deaths: 0.
num_space_committed: 1761.
num_space_uncommitted: 0.
num_chunks_returned_to_freelist: 6.
num_chunks_taken_from_freelist: 9152.
num_chunk_merges: 6.
num_chunk_splits: 6042.
num_chunks_enlarged: 3879.
num_inconsistent_stats: 0.

CodeHeap 'non-profiled nmethods': size=120000Kb used=8087Kb max_used=8578Kb free=111912Kb
 bounds [0x000001d442720000, 0x000001d442f90000, 0x000001d449c50000]
CodeHeap 'profiled nmethods': size=120000Kb used=25725Kb max_used=25725Kb free=94274Kb
 bounds [0x000001d43ac50000, 0x000001d43c5b0000, 0x000001d442180000]
CodeHeap 'non-nmethods': size=5760Kb used=2922Kb max_used=2980Kb free=2837Kb
 bounds [0x000001d442180000, 0x000001d442480000, 0x000001d442720000]
 total_blobs=14056 nmethods=13051 adapters=909
 compilation: enabled
              stopped_count=0, restarted_count=0
 full_count=0

Compilation events (20 events):
Event: 20.029 Thread 0x000001d44f2b2110 15816       3       com.android.tools.r8.internal.Ba::<init> (118 bytes)
Event: 20.030 Thread 0x000001d44f2b2110 nmethod 15816 0x000001d43b5d8490 code [0x000001d43b5d8640, 0x000001d43b5d88f0]
Event: 20.040 Thread 0x000001d49b637e30 nmethod 15790 0x000001d442e7c310 code [0x000001d442e7c540, 0x000001d442e7cc08]
Event: 20.040 Thread 0x000001d49b637e30 15792       4       com.android.tools.r8.internal.iZ::a (89 bytes)
Event: 20.040 Thread 0x000001d44f2b2110 15818       2       com.android.tools.r8.internal.h1::<init> (5 bytes)
Event: 20.040 Thread 0x000001d44f2b2110 nmethod 15818 0x000001d43b5d8110 code [0x000001d43b5d82a0, 0x000001d43b5d83a8]
Event: 20.040 Thread 0x000001d44f2b2110 15819       2       com.android.tools.r8.internal.b1::<init> (5 bytes)
Event: 20.040 Thread 0x000001d44f2b2110 nmethod 15819 0x000001d43b5d7d90 code [0x000001d43b5d7f20, 0x000001d43b5d8028]
Event: 20.042 Thread 0x000001d49b637e30 nmethod 15792 0x000001d44281ef90 code [0x000001d44281f140, 0x000001d44281f2d8]
Event: 20.042 Thread 0x000001d49b637e30 15725       4       com.android.tools.r8.graph.l1::c (60 bytes)
Event: 20.045 Thread 0x000001d49b637e30 nmethod 15725 0x000001d44281e890 code [0x000001d44281ea60, 0x000001d44281ecf8]
Event: 20.045 Thread 0x000001d49b637e30 15704       4       com.android.tools.r8.graph.t4::a (31 bytes)
Event: 20.053 Thread 0x000001d44f2b2110 15821       2       com.android.tools.r8.internal.i1::<init> (5 bytes)
Event: 20.053 Thread 0x000001d44f2b2110 nmethod 15821 0x000001d43b5d7a10 code [0x000001d43b5d7ba0, 0x000001d43b5d7ca8]
Event: 20.053 Thread 0x000001d44f2b2110 15822       2       com.android.tools.r8.internal.l30::<init> (10 bytes)
Event: 20.054 Thread 0x000001d44f2b2110 nmethod 15822 0x000001d43b5d7690 code [0x000001d43b5d7820, 0x000001d43b5d7970]
Event: 20.054 Thread 0x000001d44f2b2110 15823       2       com.android.tools.r8.internal.l30::iterator (12 bytes)
Event: 20.054 Thread 0x000001d44f2b2110 nmethod 15823 0x000001d43b5d7210 code [0x000001d43b5d73c0, 0x000001d43b5d75b0]
Event: 20.056 Thread 0x000001d44f2b2110 15824       2       com.android.tools.r8.internal.o30::entrySet (26 bytes)
Event: 20.056 Thread 0x000001d44f2b2110 nmethod 15824 0x000001d43b5d6d90 code [0x000001d43b5d6f40, 0x000001d43b5d7170]

GC Heap History (20 events):
Event: 14.934 GC heap after
{Heap after GC invocations=33 (full 0):
 garbage-first heap   total 165888K, used 106385K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 7 young (7168K), 7 survivors (7168K)
 Metaspace       used 90581K, committed 92800K, reserved 1179648K
  class space    used 12533K, committed 13632K, reserved 1048576K
}
Event: 15.352 GC heap before
{Heap before GC invocations=34 (full 0):
 garbage-first heap   total 191488K, used 146321K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 47 young (48128K), 7 survivors (7168K)
 Metaspace       used 91136K, committed 93376K, reserved 1179648K
  class space    used 12614K, committed 13760K, reserved 1048576K
}
Event: 15.359 GC heap after
{Heap after GC invocations=35 (full 0):
 garbage-first heap   total 191488K, used 108576K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 5 young (5120K), 5 survivors (5120K)
 Metaspace       used 91136K, committed 93376K, reserved 1179648K
  class space    used 12614K, committed 13760K, reserved 1048576K
}
Event: 16.244 GC heap before
{Heap before GC invocations=35 (full 0):
 garbage-first heap   total 191488K, used 171040K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 67 young (68608K), 5 survivors (5120K)
 Metaspace       used 96855K, committed 99072K, reserved 1179648K
  class space    used 13297K, committed 14400K, reserved 1048576K
}
Event: 16.251 GC heap after
{Heap after GC invocations=36 (full 0):
 garbage-first heap   total 230400K, used 109068K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 8 young (8192K), 8 survivors (8192K)
 Metaspace       used 96855K, committed 99072K, reserved 1179648K
  class space    used 13297K, committed 14400K, reserved 1048576K
}
Event: 17.049 GC heap before
{Heap before GC invocations=36 (full 0):
 garbage-first heap   total 230400K, used 205324K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 103 young (105472K), 8 survivors (8192K)
 Metaspace       used 97975K, committed 100224K, reserved 1179648K
  class space    used 13441K, committed 14592K, reserved 1048576K
}
Event: 17.059 GC heap after
{Heap after GC invocations=37 (full 0):
 garbage-first heap   total 230400K, used 115064K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 13 young (13312K), 13 survivors (13312K)
 Metaspace       used 97975K, committed 100224K, reserved 1179648K
  class space    used 13441K, committed 14592K, reserved 1048576K
}
Event: 17.756 GC heap before
{Heap before GC invocations=37 (full 0):
 garbage-first heap   total 230400K, used 198008K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 96 young (98304K), 13 survivors (13312K)
 Metaspace       used 98651K, committed 100928K, reserved 1179648K
  class space    used 13533K, committed 14656K, reserved 1048576K
}
Event: 17.768 GC heap after
{Heap after GC invocations=38 (full 0):
 garbage-first heap   total 230400K, used 117399K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 7 young (7168K), 7 survivors (7168K)
 Metaspace       used 98651K, committed 100928K, reserved 1179648K
  class space    used 13533K, committed 14656K, reserved 1048576K
}
Event: 18.102 GC heap before
{Heap before GC invocations=39 (full 0):
 garbage-first heap   total 247808K, used 210583K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 95 young (97280K), 7 survivors (7168K)
 Metaspace       used 99591K, committed 101824K, reserved 1179648K
  class space    used 13668K, committed 14784K, reserved 1048576K
}
Event: 18.113 GC heap after
{Heap after GC invocations=40 (full 0):
 garbage-first heap   total 247808K, used 133894K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 12 young (12288K), 12 survivors (12288K)
 Metaspace       used 99591K, committed 101824K, reserved 1179648K
  class space    used 13668K, committed 14784K, reserved 1048576K
}
Event: 18.700 GC heap before
{Heap before GC invocations=40 (full 0):
 garbage-first heap   total 247808K, used 218886K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 96 young (98304K), 12 survivors (12288K)
 Metaspace       used 103137K, committed 105408K, reserved 1179648K
  class space    used 14189K, committed 15296K, reserved 1048576K
}
Event: 18.713 GC heap after
{Heap after GC invocations=41 (full 0):
 garbage-first heap   total 247808K, used 142028K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 9 young (9216K), 9 survivors (9216K)
 Metaspace       used 103137K, committed 105408K, reserved 1179648K
  class space    used 14189K, committed 15296K, reserved 1048576K
}
Event: 19.061 GC heap before
{Heap before GC invocations=41 (full 0):
 garbage-first heap   total 247808K, used 221900K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 87 young (89088K), 9 survivors (9216K)
 Metaspace       used 104752K, committed 107008K, reserved 1179648K
  class space    used 14423K, committed 15552K, reserved 1048576K
}
Event: 19.078 GC heap after
{Heap after GC invocations=42 (full 0):
 garbage-first heap   total 247808K, used 156672K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 11 young (11264K), 11 survivors (11264K)
 Metaspace       used 104752K, committed 107008K, reserved 1179648K
  class space    used 14423K, committed 15552K, reserved 1048576K
}
Event: 19.421 GC heap before
{Heap before GC invocations=43 (full 0):
 garbage-first heap   total 304128K, used 220160K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 73 young (74752K), 11 survivors (11264K)
 Metaspace       used 106158K, committed 108416K, reserved 1179648K
  class space    used 14637K, committed 15744K, reserved 1048576K
}
Event: 19.445 GC heap after
{Heap after GC invocations=44 (full 0):
 garbage-first heap   total 304128K, used 169984K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 10 young (10240K), 10 survivors (10240K)
 Metaspace       used 106158K, committed 108416K, reserved 1179648K
  class space    used 14637K, committed 15744K, reserved 1048576K
}
Event: 19.764 GC heap before
{Heap before GC invocations=44 (full 0):
 garbage-first heap   total 304128K, used 264192K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 103 young (105472K), 10 survivors (10240K)
 Metaspace       used 108717K, committed 110976K, reserved 1179648K
  class space    used 15102K, committed 16256K, reserved 1048576K
}
Event: 19.785 GC heap after
{Heap after GC invocations=45 (full 0):
 garbage-first heap   total 304128K, used 184320K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 13 young (13312K), 13 survivors (13312K)
 Metaspace       used 108717K, committed 110976K, reserved 1179648K
  class space    used 15102K, committed 16256K, reserved 1048576K
}
Event: 20.060 GC heap before
{Heap before GC invocations=45 (full 0):
 garbage-first heap   total 304128K, used 269312K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 96 young (98304K), 13 survivors (13312K)
 Metaspace       used 110442K, committed 112768K, reserved 1179648K
  class space    used 15443K, committed 16576K, reserved 1048576K
}

Dll operation events (15 events):
Event: 0.008 Loaded shared library C:\Program Files\Java\jdk-21\bin\java.dll
Event: 0.038 Loaded shared library C:\Program Files\Java\jdk-21\bin\jsvml.dll
Event: 0.076 Loaded shared library C:\Program Files\Java\jdk-21\bin\zip.dll
Event: 0.079 Loaded shared library C:\Program Files\Java\jdk-21\bin\instrument.dll
Event: 0.082 Loaded shared library C:\Program Files\Java\jdk-21\bin\net.dll
Event: 0.084 Loaded shared library C:\Program Files\Java\jdk-21\bin\nio.dll
Event: 0.086 Loaded shared library C:\Program Files\Java\jdk-21\bin\zip.dll
Event: 0.333 Loaded shared library C:\Program Files\Java\jdk-21\bin\jimage.dll
Event: 0.482 Loaded shared library C:\Program Files\Java\jdk-21\bin\verify.dll
Event: 0.581 Loaded shared library C:\Users\<USER>\.gradle\native\1def1411415f61bf3af743bc5b6707747c0891f09f0c88961ee8f79bc544acac\windows-amd64\native-platform.dll
Event: 0.591 Loaded shared library C:\Users\<USER>\.gradle\native\0.2.7\x86_64-windows-gnu\gradle-fileevents.dll
Event: 1.790 Loaded shared library C:\Program Files\Java\jdk-21\bin\management.dll
Event: 1.792 Loaded shared library C:\Program Files\Java\jdk-21\bin\management_ext.dll
Event: 2.011 Loaded shared library C:\Program Files\Java\jdk-21\bin\extnet.dll
Event: 2.341 Loaded shared library C:\Program Files\Java\jdk-21\bin\sunmscapi.dll

Deoptimization events (20 events):
Event: 19.681 Thread 0x000001d4954794b0 DEOPT UNPACKING pc=0x000001d4421d46a2 sp=0x00000048490fb840 mode 2
Event: 19.681 Thread 0x000001d49adbb2d0 DEOPT UNPACKING pc=0x000001d4421d46a2 sp=0x000000484a0f89c0 mode 2
Event: 19.786 Thread 0x000001d49adbb2d0 Uncommon trap: trap_request=0xfffffff4 fr.pc=0x000001d4428a8de4 relative=0x0000000000000284
Event: 19.786 Thread 0x000001d49adbb2d0 Uncommon trap: reason=null_check action=make_not_entrant pc=0x000001d4428a8de4 method=java.lang.invoke.MethodType.equals(Ljava/lang/Object;)Z @ 39 c2
Event: 19.786 Thread 0x000001d49adbb2d0 DEOPT PACKING pc=0x000001d4428a8de4 sp=0x000000484a0f7460
Event: 19.786 Thread 0x000001d49adbb2d0 DEOPT UNPACKING pc=0x000001d4421d46a2 sp=0x000000484a0f7418 mode 2
Event: 19.919 Thread 0x000001d495473f60 DEOPT PACKING pc=0x000001d43bf31d72 sp=0x00000048493f9600
Event: 19.919 Thread 0x000001d495473f60 DEOPT UNPACKING pc=0x000001d4421d4e42 sp=0x00000048493f8ab8 mode 0
Event: 19.921 Thread 0x000001d4954794b0 DEOPT PACKING pc=0x000001d43b7264bc sp=0x00000048490fa690
Event: 19.921 Thread 0x000001d4954794b0 DEOPT UNPACKING pc=0x000001d4421d4e42 sp=0x00000048490f9b90 mode 0
Event: 19.939 Thread 0x000001d49adbb2d0 DEOPT PACKING pc=0x000001d43c2275f4 sp=0x000000484a0f8140
Event: 19.939 Thread 0x000001d49adbb2d0 DEOPT UNPACKING pc=0x000001d4421d4e42 sp=0x000000484a0f74f0 mode 0
Event: 19.941 Thread 0x000001d495473f60 DEOPT PACKING pc=0x000001d43c2275f4 sp=0x00000048493fac30
Event: 19.941 Thread 0x000001d495473f60 DEOPT UNPACKING pc=0x000001d4421d4e42 sp=0x00000048493f9fe0 mode 0
Event: 19.941 Thread 0x000001d4954794b0 DEOPT PACKING pc=0x000001d43c2275f4 sp=0x00000048490fafc0
Event: 19.941 Thread 0x000001d4954794b0 DEOPT UNPACKING pc=0x000001d4421d4e42 sp=0x00000048490fa370 mode 0
Event: 19.941 Thread 0x000001d4954738d0 DEOPT PACKING pc=0x000001d43c2275f4 sp=0x00000048496facc0
Event: 19.941 Thread 0x000001d4954738d0 DEOPT UNPACKING pc=0x000001d4421d4e42 sp=0x00000048496fa070 mode 0
Event: 19.999 Thread 0x000001d49547a860 DEOPT PACKING pc=0x000001d43c2275f4 sp=0x00000048494fac90
Event: 19.999 Thread 0x000001d49547a860 DEOPT UNPACKING pc=0x000001d4421d4e42 sp=0x00000048494fa040 mode 0

Classes loaded (20 events):
Event: 17.347 Loading class java/util/zip/ZipFile$ZipEntryIterator
Event: 17.347 Loading class java/util/zip/ZipFile$ZipEntryIterator done
Event: 17.787 Loading class java/util/zip/ZipOutputStream
Event: 17.790 Loading class java/util/zip/ZipOutputStream done
Event: 17.961 Loading class sun/nio/fs/Globs
Event: 17.961 Loading class sun/nio/fs/Globs done
Event: 17.961 Loading class java/util/regex/Pattern$SliceU
Event: 17.962 Loading class java/util/regex/Pattern$SliceU done
Event: 17.962 Loading class sun/nio/fs/WindowsFileSystem$2
Event: 17.962 Loading class sun/nio/fs/WindowsFileSystem$2 done
Event: 18.020 Loading class java/util/zip/ZipInputStream
Event: 18.020 Loading class java/util/zip/ZipInputStream done
Event: 18.165 Loading class java/io/UTFDataFormatException
Event: 18.165 Loading class java/io/UTFDataFormatException done
Event: 18.288 Loading class java/util/concurrent/RejectedExecutionException
Event: 18.288 Loading class java/util/concurrent/RejectedExecutionException done
Event: 19.317 Loading class java/util/concurrent/ConcurrentHashMap$TreeNode
Event: 19.317 Loading class java/util/concurrent/ConcurrentHashMap$TreeNode done
Event: 19.318 Loading class java/util/concurrent/ConcurrentHashMap$TreeBin
Event: 19.318 Loading class java/util/concurrent/ConcurrentHashMap$TreeBin done

Classes unloaded (0 events):
No events

Classes redefined (0 events):
No events

Internal exceptions (20 events):
Event: 19.120 Thread 0x000001d4954738d0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000095e1ae78}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeInterface(java.lang.Object, java.lang.Object, java.lang.Object, int)'> (0x0000000095e1ae78) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 19.741 Thread 0x000001d495477a70 Exception <a 'java/lang/NoSuchMethodError'{0x000000008c34b7a8}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, int, java.lang.Object, java.lang.Object)'> (0x000000008c34b7a8) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 19.741 Thread 0x000001d4954794b0 Exception <a 'java/lang/NoSuchMethodError'{0x000000008c3e8aa8}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, int, java.lang.Object, java.lang.Object)'> (0x000000008c3e8aa8) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 19.741 Thread 0x000001d49adbb2d0 Exception <a 'java/lang/NoSuchMethodError'{0x000000008c5b52c8}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, int, java.lang.Object, java.lang.Object)'> (0x000000008c5b52c8) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 19.741 Thread 0x000001d495473f60 Exception <a 'java/lang/NoSuchMethodError'{0x000000008c4b6218}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, int, java.lang.Object, java.lang.Object)'> (0x000000008c4b6218) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 19.741 Thread 0x000001d4954738d0 Exception <a 'java/lang/NoSuchMethodError'{0x000000008c49c040}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, int, java.lang.Object, java.lang.Object)'> (0x000000008c49c040) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 19.741 Thread 0x000001d495478790 Exception <a 'java/lang/NoSuchMethodError'{0x000000008c39a2e0}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, int, java.lang.Object, java.lang.Object)'> (0x000000008c39a2e0) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 19.744 Thread 0x000001d495477a70 Exception <a 'java/lang/NoSuchMethodError'{0x000000008c24d638}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object, int, java.lang.Object, java.lang.Object)'> (0x000000008c24d638) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 19.744 Thread 0x000001d4954794b0 Exception <a 'java/lang/NoSuchMethodError'{0x000000008c3fa300}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object, int, java.lang.Object, java.lang.Object)'> (0x000000008c3fa300) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 19.744 Thread 0x000001d49adbb2d0 Exception <a 'java/lang/NoSuchMethodError'{0x000000008c5c6cd8}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object, int, java.lang.Object, java.lang.Object)'> (0x000000008c5c6cd8) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 19.744 Thread 0x000001d4954738d0 Exception <a 'java/lang/NoSuchMethodError'{0x000000008c259a48}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object, int, java.lang.Object, java.lang.Object)'> (0x000000008c259a48) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 19.744 Thread 0x000001d495473f60 Exception <a 'java/lang/NoSuchMethodError'{0x000000008c286af8}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object, int, java.lang.Object, java.lang.Object)'> (0x000000008c286af8) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 19.744 Thread 0x000001d495478790 Exception <a 'java/lang/NoSuchMethodError'{0x000000008c278860}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object, int, java.lang.Object, java.lang.Object)'> (0x000000008c278860) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 19.786 Thread 0x000001d49adbb2d0 Implicit null exception at 0x000001d4428a8bef to 0x000001d4428a8dd4
Event: 19.864 Thread 0x000001d4954794b0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000091d19908}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, int)'> (0x0000000091d19908) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 19.864 Thread 0x000001d49adbb2d0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000091d5b6f8}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, int)'> (0x0000000091d5b6f8) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 19.864 Thread 0x000001d495478790 Exception <a 'java/lang/NoSuchMethodError'{0x0000000091f88620}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, int)'> (0x0000000091f88620) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 19.864 Thread 0x000001d49547a860 Exception <a 'java/lang/NoSuchMethodError'{0x0000000095ebf438}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, int)'> (0x0000000095ebf438) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 20.048 Thread 0x000001d495477a70 Exception <a 'java/lang/IncompatibleClassChangeError'{0x000000008e073be8}: Found class java.lang.Object, but interface was expected> (0x000000008e073be8) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 840]
Event: 20.048 Thread 0x000001d495478790 Exception <a 'java/lang/IncompatibleClassChangeError'{0x000000008d762a90}: Found class java.lang.Object, but interface was expected> (0x000000008d762a90) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 840]

ZGC Phase Switch (0 events):
No events

VM Operations (20 events):
Event: 19.595 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 19.763 Executing VM operation: G1CollectForAllocation (G1 Evacuation Pause)
Event: 19.788 Executing VM operation: G1CollectForAllocation (G1 Evacuation Pause) done
Event: 19.847 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 19.847 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 19.849 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 19.850 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 19.875 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 19.876 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 19.883 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 19.884 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 19.939 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 19.955 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 20.007 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 20.015 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 20.033 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 20.045 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 20.050 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 20.060 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 20.060 Executing VM operation: G1CollectForAllocation (G1 Evacuation Pause)

Events (20 events):
Event: 19.239 Thread 0x000001d44f1d7670 flushing nmethod 0x000001d44299c110
Event: 19.239 Thread 0x000001d44f1d7670 flushing nmethod 0x000001d442a0ce90
Event: 19.239 Thread 0x000001d44f1d7670 flushing nmethod 0x000001d4429f7f10
Event: 19.239 Thread 0x000001d44f1d7670 flushing nmethod 0x000001d4429e1310
Event: 19.239 Thread 0x000001d44f1d7670 flushing nmethod 0x000001d4429dff10
Event: 19.239 Thread 0x000001d44f1d7670 flushing nmethod 0x000001d4429c5a10
Event: 19.240 Thread 0x000001d44f1d7670 flushing nmethod 0x000001d44298c110
Event: 19.240 Thread 0x000001d44f1d7670 flushing nmethod 0x000001d442955010
Event: 19.240 Thread 0x000001d44f1d7670 flushing nmethod 0x000001d4428f9a90
Event: 19.240 Thread 0x000001d44f1d7670 flushing nmethod 0x000001d44288b710
Event: 19.240 Thread 0x000001d44f1d7670 flushing nmethod 0x000001d442914510
Event: 19.240 Thread 0x000001d44f1d7670 flushing nmethod 0x000001d4428f5810
Event: 19.240 Thread 0x000001d44f1d7670 flushing nmethod 0x000001d4428c4790
Event: 19.240 Thread 0x000001d44f1d7670 flushing nmethod 0x000001d4428ab210
Event: 19.240 Thread 0x000001d44f1d7670 flushing nmethod 0x000001d4427bf310
Event: 19.240 Thread 0x000001d44f1d7670 flushing nmethod 0x000001d4427f8a90
Event: 19.240 Thread 0x000001d44f1d7670 flushing nmethod 0x000001d4427cbc90
Event: 19.240 Thread 0x000001d44f1d7670 flushing nmethod 0x000001d44281e010
Event: 19.240 Thread 0x000001d44f1d7670 flushing nmethod 0x000001d44274ec10
Event: 19.240 Thread 0x000001d44f1d7670 flushing nmethod 0x000001d44274a410


Dynamic libraries:
0x00007ff60bce0000 - 0x00007ff60bcf0000 	C:\Program Files\Java\jdk-21\bin\java.exe
0x00007ff8c9510000 - 0x00007ff8c9708000 	C:\WINDOWS\SYSTEM32\ntdll.dll
0x00007ff8c85c0000 - 0x00007ff8c8682000 	C:\WINDOWS\System32\KERNEL32.DLL
0x00007ff8c6f40000 - 0x00007ff8c7236000 	C:\WINDOWS\System32\KERNELBASE.dll
0x00007ff8c6c40000 - 0x00007ff8c6d40000 	C:\WINDOWS\System32\ucrtbase.dll
0x00007ff8aca70000 - 0x00007ff8aca89000 	C:\Program Files\Java\jdk-21\bin\jli.dll
0x00007ff8b7b10000 - 0x00007ff8b7b2b000 	C:\Program Files\Java\jdk-21\bin\VCRUNTIME140.dll
0x00007ff8c7a20000 - 0x00007ff8c7ad1000 	C:\WINDOWS\System32\ADVAPI32.dll
0x00007ff8c9140000 - 0x00007ff8c91de000 	C:\WINDOWS\System32\msvcrt.dll
0x00007ff8c8520000 - 0x00007ff8c85bf000 	C:\WINDOWS\System32\sechost.dll
0x00007ff8c8b30000 - 0x00007ff8c8c53000 	C:\WINDOWS\System32\RPCRT4.dll
0x00007ff8c6f10000 - 0x00007ff8c6f37000 	C:\WINDOWS\System32\bcrypt.dll
0x00007ff8c8810000 - 0x00007ff8c89ad000 	C:\WINDOWS\System32\USER32.dll
0x00007ff8c6ee0000 - 0x00007ff8c6f02000 	C:\WINDOWS\System32\win32u.dll
0x00007ff8b61b0000 - 0x00007ff8b644a000 	C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.19041.5912_none_60b4fe2971f97ae4\COMCTL32.dll
0x00007ff8c90b0000 - 0x00007ff8c90db000 	C:\WINDOWS\System32\GDI32.dll
0x00007ff8c6dc0000 - 0x00007ff8c6ed9000 	C:\WINDOWS\System32\gdi32full.dll
0x00007ff8c6ba0000 - 0x00007ff8c6c3d000 	C:\WINDOWS\System32\msvcp_win.dll
0x00007ff8bebf0000 - 0x00007ff8bebfa000 	C:\WINDOWS\SYSTEM32\VERSION.dll
0x00007ff8c8fc0000 - 0x00007ff8c8fef000 	C:\WINDOWS\System32\IMM32.DLL
0x00007ff8c2ba0000 - 0x00007ff8c2bac000 	C:\Program Files\Java\jdk-21\bin\vcruntime140_1.dll
0x00007ff89c610000 - 0x00007ff89c69e000 	C:\Program Files\Java\jdk-21\bin\msvcp140.dll
0x00007ff889fc0000 - 0x00007ff88acd7000 	C:\Program Files\Java\jdk-21\bin\server\jvm.dll
0x00007ff8c7ae0000 - 0x00007ff8c7b4b000 	C:\WINDOWS\System32\WS2_32.dll
0x00007ff8c6a00000 - 0x00007ff8c6a4b000 	C:\WINDOWS\SYSTEM32\POWRPROF.dll
0x00007ff8bb860000 - 0x00007ff8bb887000 	C:\WINDOWS\SYSTEM32\WINMM.dll
0x00007ff8c69e0000 - 0x00007ff8c69f2000 	C:\WINDOWS\SYSTEM32\UMPDC.dll
0x00007ff8c53f0000 - 0x00007ff8c5402000 	C:\WINDOWS\SYSTEM32\kernel.appcore.dll
0x00007ff8c2b20000 - 0x00007ff8c2b2a000 	C:\Program Files\Java\jdk-21\bin\jimage.dll
0x00007ff8c51a0000 - 0x00007ff8c53a1000 	C:\WINDOWS\SYSTEM32\DBGHELP.DLL
0x00007ff8b5010000 - 0x00007ff8b5044000 	C:\WINDOWS\SYSTEM32\dbgcore.DLL
0x00007ff8c74a0000 - 0x00007ff8c7522000 	C:\WINDOWS\System32\bcryptPrimitives.dll
0x00007ff8abd50000 - 0x00007ff8abd5f000 	C:\Program Files\Java\jdk-21\bin\instrument.dll
0x00007ff8aca30000 - 0x00007ff8aca4f000 	C:\Program Files\Java\jdk-21\bin\java.dll
0x00007ff8c7b50000 - 0x00007ff8c82be000 	C:\WINDOWS\System32\SHELL32.dll
0x00007ff8c49f0000 - 0x00007ff8c5193000 	C:\WINDOWS\SYSTEM32\windows.storage.dll
0x00007ff8c8c60000 - 0x00007ff8c8fb3000 	C:\WINDOWS\System32\combase.dll
0x00007ff8c6500000 - 0x00007ff8c652b000 	C:\WINDOWS\SYSTEM32\Wldp.dll
0x00007ff8c82c0000 - 0x00007ff8c838d000 	C:\WINDOWS\System32\OLEAUT32.dll
0x00007ff8c8390000 - 0x00007ff8c843d000 	C:\WINDOWS\System32\SHCORE.dll
0x00007ff8c87b0000 - 0x00007ff8c880b000 	C:\WINDOWS\System32\shlwapi.dll
0x00007ff8c6ad0000 - 0x00007ff8c6af5000 	C:\WINDOWS\SYSTEM32\profapi.dll
0x00007ff89c530000 - 0x00007ff89c607000 	C:\Program Files\Java\jdk-21\bin\jsvml.dll
0x00007ff8a9290000 - 0x00007ff8a92a8000 	C:\Program Files\Java\jdk-21\bin\zip.dll
0x00007ff8c3ad0000 - 0x00007ff8c3ae0000 	C:\Program Files\Java\jdk-21\bin\net.dll
0x00007ff8c2e30000 - 0x00007ff8c2f3a000 	C:\WINDOWS\SYSTEM32\WINHTTP.dll
0x00007ff8c6260000 - 0x00007ff8c62ca000 	C:\WINDOWS\system32\mswsock.dll
0x00007ff8ac830000 - 0x00007ff8ac846000 	C:\Program Files\Java\jdk-21\bin\nio.dll
0x00007ff8bf2f0000 - 0x00007ff8bf300000 	C:\Program Files\Java\jdk-21\bin\verify.dll
0x00007ff89d3f0000 - 0x00007ff89d417000 	C:\Users\<USER>\.gradle\native\1def1411415f61bf3af743bc5b6707747c0891f09f0c88961ee8f79bc544acac\windows-amd64\native-platform.dll
0x00007ff89d1e0000 - 0x00007ff89d258000 	C:\Users\<USER>\.gradle\native\0.2.7\x86_64-windows-gnu\gradle-fileevents.dll
0x00007ff8be820000 - 0x00007ff8be82a000 	C:\Program Files\Java\jdk-21\bin\management.dll
0x00007ff8b7b00000 - 0x00007ff8b7b0b000 	C:\Program Files\Java\jdk-21\bin\management_ext.dll
0x00007ff8c94c0000 - 0x00007ff8c94c8000 	C:\WINDOWS\System32\PSAPI.DLL
0x00007ff8c5f40000 - 0x00007ff8c5f7b000 	C:\WINDOWS\SYSTEM32\IPHLPAPI.DLL
0x00007ff8c90a0000 - 0x00007ff8c90a8000 	C:\WINDOWS\System32\NSI.dll
0x00007ff8ac6c0000 - 0x00007ff8ac6c9000 	C:\Program Files\Java\jdk-21\bin\extnet.dll
0x00007ff8c6450000 - 0x00007ff8c6468000 	C:\WINDOWS\SYSTEM32\CRYPTSP.dll
0x00007ff8c5b80000 - 0x00007ff8c5bb8000 	C:\WINDOWS\system32\rsaenh.dll
0x00007ff8c6a50000 - 0x00007ff8c6a7e000 	C:\WINDOWS\SYSTEM32\USERENV.dll
0x00007ff8c6470000 - 0x00007ff8c647c000 	C:\WINDOWS\SYSTEM32\CRYPTBASE.dll
0x00007ff8a9520000 - 0x00007ff8a952e000 	C:\Program Files\Java\jdk-21\bin\sunmscapi.dll
0x00007ff8c7240000 - 0x00007ff8c739d000 	C:\WINDOWS\System32\CRYPT32.dll
0x00007ff8c6570000 - 0x00007ff8c6597000 	C:\WINDOWS\SYSTEM32\ncrypt.dll
0x00007ff8c6530000 - 0x00007ff8c656b000 	C:\WINDOWS\SYSTEM32\NTASN1.dll
0x00007ff8ad2c0000 - 0x00007ff8ad2c7000 	C:\WINDOWS\system32\wshunix.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;C:\Program Files\Java\jdk-21\bin;C:\WINDOWS\SYSTEM32;C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.19041.5912_none_60b4fe2971f97ae4;C:\Program Files\Java\jdk-21\bin\server;C:\Users\<USER>\.gradle\native\1def1411415f61bf3af743bc5b6707747c0891f09f0c88961ee8f79bc544acac\windows-amd64;C:\Users\<USER>\.gradle\native\0.2.7\x86_64-windows-gnu

VM Arguments:
jvm_args: --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.api=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.util=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-opens=java.base/java.nio.charset=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED --add-opens=java.xml/javax.xml.namespace=ALL-UNNAMED -Xmx2048m -Dfile.encoding=UTF-8 -Duser.country=US -Duser.language=en -Duser.variant -javaagent:C:\Users\<USER>\.gradle\wrapper\dists\gradle-8.13-bin\5xuhj0ry160q40clulazy9h7d\gradle-8.13\lib\agents\gradle-instrumentation-agent-8.13.jar 
java_command: org.gradle.launcher.daemon.bootstrap.GradleDaemon 8.13
java_class_path (initial): C:\Users\<USER>\.gradle\wrapper\dists\gradle-8.13-bin\5xuhj0ry160q40clulazy9h7d\gradle-8.13\lib\gradle-daemon-main-8.13.jar
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 4                                         {product} {ergonomic}
     uint ConcGCThreads                            = 2                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 8                                         {product} {ergonomic}
   size_t G1HeapRegionSize                         = 1048576                                   {product} {ergonomic}
    uintx GCDrainStackTargetSize                   = 64                                        {product} {ergonomic}
   size_t InitialHeapSize                          = 369098752                                 {product} {ergonomic}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MaxHeapSize                              = 2147483648                                {product} {command line}
   size_t MaxNewSize                               = 1287651328                                {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 1048576                                   {product} {ergonomic}
   size_t MinHeapSize                              = 8388608                                   {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 5839372                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122909434                              {pd product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122909434                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 2147483648                             {manageable} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags foldmultilines=false
 #1: stderr all=off uptime,level,tags foldmultilines=false

Environment Variables:
PATH=C:\Users\<USER>\bin;C:\Program Files\Git\mingw64\bin;C:\Program Files\Git\usr\local\bin;C:\Program Files\Git\usr\bin;C:\Program Files\Git\usr\bin;C:\Program Files\Git\mingw64\bin;C:\Program Files\Git\usr\bin;C:\Users\<USER>\bin;C:\Program Files\Python313\Scripts;C:\Program Files\Python313;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Program Files\Common Files\Oracle\Java\javapath;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0;C:\xampp\php;C:\flutter sdk\flutter\bin;C:\Program Files\Java\jdk-21\bin;C:\OpenSSH-Win64\OpenSSH-Win64;C:\Windows\System32;C:\Program Files\Git\cmd;C:\Program Files\Git\usr\bin;C:\Users\<USER>\AppData\Local\Android\Sdk\platform-tools;C:\xampp\php\php.exe;C:\Program Files (x86)\Windows Kits\10\Windows Performance Toolkit;C:\scrcpy;C:\ProgramData\ComposerSetup\bin;C:\Program Files\nodejs;C:\Program Files\Common Files\Oracle\Java\javapath;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0;C:\xampp\php;C:\Program Files\Java\jdk1.8.0_111\bin;C:\Program Files (x86)\Windows Kits\10\Windows Performance Toolkit;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Program Files\JetBrains\PyCharm Community Edition 2022.1.3\bin;C:\Program Files\Java\jdk1.8.0_291\bin;C:\;C:\Users\<USER>\AppData\Roaming\Microsoft\Windows\Start Menu\Programs\Python 3.13;C:\Program Files\Void\bin;C:\Program Files\Common Files\Oracle\Java\javapath;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0;C:\xampp\php;C:\Program Files\Java\jdk1.8.0_111\bin;C:\Program Files (x86)\Windows Kits\10\Windows Performance Toolkit;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Program Files\JetBrains\PyCharm Community Edition 2022.1.3\bin;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Program Files\Java\jdk1.8.0_291\bin;C:\Program Files\JetBrains\PhpStorm 2024.1.3\bin;C:\dart-sdk\bin;C:\flutter sdk\flutter\bin;C:\Users\<USER>\AppData\Roaming\Composer\vendor\bin;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\AppData\Local\Microsoft\WinGet\Packages\Genymobile.scrcpy_Microsoft.Winget.Source_8wekyb3d8bbwe\scrcpy-win64-v3.1;C:\Users\<USER>\AppData\Local\Programs\Windsurf\bin;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\github.copilot-chat\debugCommand;C:\Program Files\Git\usr\bin\vendor_perl;C:\Program Files\Git\usr\bin\core_perl
USERNAME=ntc
SHELL=C:\Program Files\Git\usr\bin\bash.exe
DISPLAY=needs-to-be-defined
LANG=en_US.UTF-8
TERM=xterm-256color
TMPDIR=C:\Users\<USER>\AppData\Local\Temp
OS=Windows_NT
PROCESSOR_IDENTIFIER=AMD64 Family 23 Model 24 Stepping 1, AuthenticAMD
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp




Periodic native trim disabled

---------------  S Y S T E M  ---------------

OS:
 Windows 10 , 64 bit Build 19041 (10.0.19041.5912)
OS uptime: 0 days 6:36 hours

CPU: total 8 (initial active 8) (8 cores per cpu, 2 threads per core) family 23 model 24 stepping 1 microcode 0x0, cx8, cmov, fxsr, ht, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4a, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, clmul, bmi1, bmi2, adx, sha, fma, vzeroupper, clflush, clflushopt, rdtscp, f16c
Processor Information for all 8 processors :
  Max Mhz: 3700, Current Mhz: 3700, Mhz Limit: 3700

Memory: 4k page, system-wide physical 22476M (4101M free)
TotalPageFile size 22476M (AvailPageFile size 46M)
current process WorkingSet (physical memory assigned to process): 649M, peak: 649M
current process commit charge ("private bytes"): 695M, peak: 752M

vm_info: Java HotSpot(TM) 64-Bit Server VM (21.0.2+13-LTS-58) for windows-amd64 JRE (21.0.2+13-LTS-58), built on 2024-01-05T18:32:24Z by "mach5one" with MS VC++ 17.1 (VS2022)

END.
