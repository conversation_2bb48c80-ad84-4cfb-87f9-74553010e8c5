package com.anginatech.textrepeater;

import android.app.Activity;
import android.app.Application;
import android.content.Context;
import android.content.SharedPreferences;
import android.os.Bundle;
import android.util.Log;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.lifecycle.DefaultLifecycleObserver;
import androidx.lifecycle.LifecycleOwner;
import androidx.lifecycle.ProcessLifecycleOwner;

import com.google.android.gms.ads.AdError;
import com.google.android.gms.ads.AdRequest;
import com.google.android.gms.ads.FullScreenContentCallback;
import com.google.android.gms.ads.LoadAdError;
import com.google.android.gms.ads.appopen.AppOpenAd;

import java.util.Date;

/**
 * Manages App Open Ads for the Text Repeater application
 */
public class AppOpenAdManager implements Application.ActivityLifecycleCallbacks, DefaultLifecycleObserver {

    private static final String TAG = "AppOpenAdManager";
    private static final String PREFS_NAME = "AppOpenAdPrefs";
    private static final String KEY_LAST_SHOWN = "last_shown_time";
    private static final String KEY_SHOW_COUNT = "show_count";
    private static final long MIN_INTERVAL_BETWEEN_ADS = 4 * 60 * 1000; // 4 minutes
    private static final int MAX_ADS_PER_SESSION = 3;

    private AppOpenAd appOpenAd = null;
    private boolean isLoadingAd = false;
    private boolean isShowingAd = false;
    private long loadTime = 0;
    private Activity currentActivity;
    private MyApplication myApplication;

    // Ad unit ID - will be loaded from server
    private String appOpenAdUnitId = "";

    public AppOpenAdManager(MyApplication myApplication) {
        this.myApplication = myApplication;
        this.myApplication.registerActivityLifecycleCallbacks(this);
        ProcessLifecycleOwner.get().getLifecycle().addObserver(this);
    }

    /**
     * Set the App Open Ad unit ID from server configuration
     */
    public void setAppOpenAdUnitId(String adUnitId) {
        this.appOpenAdUnitId = adUnitId;
        Log.d(TAG, "App Open Ad Unit ID set: " + adUnitId);
    }

    /**
     * Load an App Open Ad
     */
    public void loadAd(Context context) {
        if (isLoadingAd || isAdAvailable() || !AdsHelper.isAds || appOpenAdUnitId.isEmpty()) {
            Log.d(TAG, "Ad not loaded - isLoading: " + isLoadingAd +
                      ", isAvailable: " + isAdAvailable() +
                      ", adsEnabled: " + AdsHelper.isAds +
                      ", adUnitId: " + appOpenAdUnitId);
            return;
        }

        isLoadingAd = true;
        AdRequest request = new AdRequest.Builder().build();

        AppOpenAd.load(
            context,
            appOpenAdUnitId,
            request,
            new AppOpenAd.AppOpenAdLoadCallback() {
                @Override
                public void onAdLoaded(@NonNull AppOpenAd ad) {
                    Log.d(TAG, "App Open Ad loaded successfully");
                    appOpenAd = ad;
                    isLoadingAd = false;
                    loadTime = new Date().getTime();

                    // Track ad load event
                    trackAdEvent(context, "app_open", "load", appOpenAdUnitId);
                }

                @Override
                public void onAdFailedToLoad(@NonNull LoadAdError loadAdError) {
                    Log.e(TAG, "App Open Ad failed to load: " + loadAdError.getMessage());
                    isLoadingAd = false;

                    // Track ad load failure
                    trackAdEvent(context, "app_open", "fail", appOpenAdUnitId);
                }
            }
        );
    }

    /**
     * Check if an ad is available and not expired
     */
    private boolean isAdAvailable() {
        return appOpenAd != null && wasLoadTimeLessThanNHoursAgo(4);
    }

    /**
     * Check if the ad was loaded less than n hours ago
     */
    private boolean wasLoadTimeLessThanNHoursAgo(long numHours) {
        long dateDifference = new Date().getTime() - loadTime;
        long numMilliSecondsPerHour = 3600000;
        return (dateDifference < (numMilliSecondsPerHour * numHours));
    }

    /**
     * Show the App Open Ad if available and conditions are met
     */
    public void showAdIfAvailable(@NonNull Activity activity) {
        showAdIfAvailable(activity, null);
    }

    /**
     * Show the App Open Ad with callback
     */
    public void showAdIfAvailable(@NonNull Activity activity, @Nullable OnShowAdCompleteListener onShowAdCompleteListener) {
        if (isShowingAd) {
            Log.d(TAG, "App Open Ad is already showing");
            return;
        }

        if (!isAdAvailable()) {
            Log.d(TAG, "App Open Ad is not ready yet");
            if (onShowAdCompleteListener != null) {
                onShowAdCompleteListener.onShowAdComplete();
            }
            loadAd(activity);
            return;
        }

        if (!shouldShowAd(activity)) {
            Log.d(TAG, "App Open Ad should not be shown based on frequency rules");
            if (onShowAdCompleteListener != null) {
                onShowAdCompleteListener.onShowAdComplete();
            }
            return;
        }

        Log.d(TAG, "Showing App Open Ad");

        appOpenAd.setFullScreenContentCallback(new FullScreenContentCallback() {
            @Override
            public void onAdDismissedFullScreenContent() {
                Log.d(TAG, "App Open Ad dismissed");
                appOpenAd = null;
                isShowingAd = false;

                // Update show statistics
                updateShowStatistics(activity);

                if (onShowAdCompleteListener != null) {
                    onShowAdCompleteListener.onShowAdComplete();
                }

                // Load next ad
                loadAd(activity);
            }

            @Override
            public void onAdFailedToShowFullScreenContent(@NonNull AdError adError) {
                Log.e(TAG, "App Open Ad failed to show: " + adError.getMessage());
                appOpenAd = null;
                isShowingAd = false;

                if (onShowAdCompleteListener != null) {
                    onShowAdCompleteListener.onShowAdComplete();
                }

                // Load next ad
                loadAd(activity);
            }

            @Override
            public void onAdShowedFullScreenContent() {
                Log.d(TAG, "App Open Ad showed successfully");
                isShowingAd = true;

                // Track ad impression
                trackAdEvent(activity, "app_open", "impression", appOpenAdUnitId);
            }

            @Override
            public void onAdClicked() {
                Log.d(TAG, "App Open Ad clicked");

                // Track ad click
                trackAdEvent(activity, "app_open", "click", appOpenAdUnitId);
            }
        });

        appOpenAd.show(activity);
    }

    /**
     * Check if ad should be shown based on frequency rules
     */
    private boolean shouldShowAd(Context context) {
        SharedPreferences prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE);

        // Check time interval
        long lastShown = prefs.getLong(KEY_LAST_SHOWN, 0);
        long currentTime = System.currentTimeMillis();

        if (currentTime - lastShown < MIN_INTERVAL_BETWEEN_ADS) {
            Log.d(TAG, "Too soon to show another ad. Last shown: " +
                      ((currentTime - lastShown) / 1000) + " seconds ago");
            return false;
        }

        // Check session count
        int showCount = prefs.getInt(KEY_SHOW_COUNT, 0);
        if (showCount >= MAX_ADS_PER_SESSION) {
            Log.d(TAG, "Maximum ads per session reached: " + showCount);
            return false;
        }

        return true;
    }

    /**
     * Update show statistics
     */
    private void updateShowStatistics(Context context) {
        SharedPreferences prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE);
        SharedPreferences.Editor editor = prefs.edit();

        editor.putLong(KEY_LAST_SHOWN, System.currentTimeMillis());
        editor.putInt(KEY_SHOW_COUNT, prefs.getInt(KEY_SHOW_COUNT, 0) + 1);
        editor.apply();

        Log.d(TAG, "Updated show statistics - Count: " + prefs.getInt(KEY_SHOW_COUNT, 0));
    }

    /**
     * Reset session statistics (call when app starts)
     */
    public void resetSessionStatistics(Context context) {
        SharedPreferences prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE);
        SharedPreferences.Editor editor = prefs.edit();
        editor.putInt(KEY_SHOW_COUNT, 0);
        editor.apply();

        Log.d(TAG, "Session statistics reset");
    }

    /**
     * Track ad events to server
     */
    private void trackAdEvent(Context context, String adType, String eventType, String adUnitId) {
        // This will be implemented to send data to your admin panel API
        Log.d(TAG, "Tracking ad event - Type: " + adType + ", Event: " + eventType + ", Unit: " + adUnitId);

        // You can implement API call here to track ad events
        // Example: ApiHelper.trackAdEvent(adType, eventType, adUnitId);
    }

    // Activity Lifecycle Callbacks
    @Override
    public void onActivityCreated(@NonNull Activity activity, @Nullable Bundle savedInstanceState) {}

    @Override
    public void onActivityStarted(@NonNull Activity activity) {
        if (!isShowingAd) {
            currentActivity = activity;
        }
    }

    @Override
    public void onActivityResumed(@NonNull Activity activity) {
        if (!isShowingAd) {
            currentActivity = activity;
        }
    }

    @Override
    public void onActivityPaused(@NonNull Activity activity) {}

    @Override
    public void onActivityStopped(@NonNull Activity activity) {}

    @Override
    public void onActivitySaveInstanceState(@NonNull Activity activity, @NonNull Bundle outState) {}

    @Override
    public void onActivityDestroyed(@NonNull Activity activity) {}

    // Lifecycle Observer Methods
    @Override
    public void onStart(@NonNull LifecycleOwner owner) {
        if (currentActivity != null) {
            showAdIfAvailable(currentActivity);
        }
        Log.d(TAG, "App moved to foreground");
    }

    /**
     * Interface for ad show completion callback
     */
    public interface OnShowAdCompleteListener {
        void onShowAdComplete();
    }
}
