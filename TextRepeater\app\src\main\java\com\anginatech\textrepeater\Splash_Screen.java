package com.anginatech.textrepeater;

import androidx.activity.EdgeToEdge;
import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.app.AppCompatDelegate;
import androidx.core.graphics.Insets;
import androidx.core.view.ViewCompat;
import androidx.core.view.WindowInsetsCompat;

import android.content.Intent;
import android.content.SharedPreferences;
import android.os.Bundle;
import android.os.Handler;
import android.util.Log;
import android.widget.Toast;

import org.json.JSONObject;

public class Splash_Screen extends AppCompatActivity {

    private static final String TAG = "Splash_Screen";
    private static final int SPLASH_SCREEN_TIMEOUT = 1000;
    private static final int MAX_SPLASH_TIMEOUT = 5000; // Maximum time to wait for ads

    private boolean isAdLoaded = false;
    private boolean isConfigLoaded = false;
    private boolean hasNavigated = false;
    private Handler splashHandler;
    private Runnable navigationRunnable;


    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        EdgeToEdge.enable(this);
        setContentView(R.layout.activity_splash_screen);
        ViewCompat.setOnApplyWindowInsetsListener(findViewById(R.id.main), (v, insets) -> {
            Insets systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars());
            v.setPadding(systemBars.left, systemBars.top, systemBars.right, systemBars.bottom);
            return insets;
        });

        // Apply night mode setting
        SharedPreferences sharedPreferences = getSharedPreferences(Config.PREFS_NIGHT_MODE, MODE_PRIVATE);
        boolean isNightMode = sharedPreferences.getBoolean("nightMode", false);

        if (isNightMode) {
            AppCompatDelegate.setDefaultNightMode(AppCompatDelegate.MODE_NIGHT_YES);
        } else {
            AppCompatDelegate.setDefaultNightMode(AppCompatDelegate.MODE_NIGHT_NO);
        }

        // Initialize splash handler
        splashHandler = new Handler();

        // Setup navigation runnable
        navigationRunnable = this::navigateToNextActivity;

        // Start loading configuration and ads
        loadAppConfiguration();

        // Set maximum timeout for splash screen
        splashHandler.postDelayed(() -> {
            if (!hasNavigated) {
                Log.w(TAG, "Maximum splash timeout reached, navigating anyway");
                navigateToNextActivity();
            }
        }, MAX_SPLASH_TIMEOUT);








    }

    /**
     * Load app configuration from API with modern repository for faster loading
     */
    private void loadAppConfiguration() {
        Log.d(TAG, "Loading app configuration with modern repository...");

        // Use modern repository for faster, cached responses
        MyApplication app = (MyApplication) getApplication();
        com.anginatech.textrepeater.repository.TextRepeaterRepository repository = app.getRepository();

        if (repository != null) {
            // Load app settings with caching
            repository.fetchAppSettings(new com.anginatech.textrepeater.repository.TextRepeaterRepository.RepositoryCallback<com.anginatech.textrepeater.models.AppSettingsResponse>() {
                @Override
                public void onSuccess(com.anginatech.textrepeater.models.AppSettingsResponse settings) {
                    if (settings.isMaintenanceMode()) {
                        showMaintenanceDialog("The app is currently under maintenance. Please try again later.");
                        return;
                    }

                    // Load AdMob configuration with modern repository
                    loadAdMobConfigurationModern();
                }

                @Override
                public void onError(String error) {
                    Log.e(TAG, "Error loading app settings: " + error);
                    // Fallback to legacy method
                    loadAppConfigurationLegacy();
                }
            });
        } else {
            // Fallback to legacy method
            loadAppConfigurationLegacy();
        }
    }

    /**
     * Legacy app configuration loading (fallback)
     */
    private void loadAppConfigurationLegacy() {
        Log.d(TAG, "Using legacy app configuration loading...");

        ApiClient apiClient = ApiClient.getInstance(this);

        // Check maintenance status first
        apiClient.checkMaintenanceStatus((isMaintenanceMode, message) -> {
            if (isMaintenanceMode) {
                showMaintenanceDialog(message);
                return;
            }

            // Load AdMob configuration
            loadAdMobConfiguration();
        });
    }

    /**
     * Modern AdMob configuration loading with caching for faster performance
     */
    private void loadAdMobConfigurationModern() {
        Log.d(TAG, "Loading AdMob configuration with modern repository...");

        MyApplication app = (MyApplication) getApplication();
        com.anginatech.textrepeater.repository.TextRepeaterRepository repository = app.getRepository();

        repository.fetchAdMobConfig(new com.anginatech.textrepeater.repository.TextRepeaterRepository.RepositoryCallback<com.anginatech.textrepeater.models.AdConfigResponse>() {
            @Override
            public void onSuccess(com.anginatech.textrepeater.models.AdConfigResponse config) {
                Log.d(TAG, "AdMob config loaded successfully with modern repository");
                isConfigLoaded = true;

                if (config.isAdsEnabled() && !config.getAppOpenId().isEmpty()) {
                    Log.d(TAG, "App Open Ad enabled, attempting to show ad");
                    showAppOpenAd();
                } else {
                    Log.d(TAG, "App Open Ad disabled or no ad unit ID");
                    isAdLoaded = true;
                    checkAndNavigate();
                }
            }

            @Override
            public void onError(String error) {
                Log.e(TAG, "Error loading AdMob config with modern repository: " + error);
                // Fallback to legacy method
                loadAdMobConfiguration();
            }
        });
    }

    /**
     * Legacy AdMob configuration loading (fallback)
     */
    private void loadAdMobConfiguration() {
        Log.d(TAG, "Loading AdMob configuration...");

        ApiClient apiClient = ApiClient.getInstance(this);
        apiClient.fetchAdMobConfig(new ApiClient.AdMobConfigCallback() {
            @Override
            public void onSuccess(JSONObject config) {
                Log.d(TAG, "AdMob config loaded successfully");
                isConfigLoaded = true;

                boolean adsEnabled = config.optInt("is_active", 1) == 1;
                String appOpenAdId = config.optString("app_open_id", "");

                if (adsEnabled && !appOpenAdId.isEmpty()) {
                    Log.d(TAG, "App Open Ad enabled, attempting to show ad");
                    showAppOpenAd();
                } else {
                    Log.d(TAG, "App Open Ad disabled or no ad unit ID");
                    isAdLoaded = true;
                    checkAndNavigate();
                }
            }

            @Override
            public void onError(String error) {
                Log.e(TAG, "Error loading AdMob config: " + error);
                isConfigLoaded = true;
                isAdLoaded = true; // Skip ad loading on error
                checkAndNavigate();
            }
        });
    }

    /**
     * Show app open ad if available
     */
    private void showAppOpenAd() {
        MyApplication app = (MyApplication) getApplication();
        AppOpenAdManager adManager = app.getAppOpenAdManager();

        if (adManager != null) {
            adManager.showAdIfAvailable(this, new AppOpenAdManager.OnShowAdCompleteListener() {
                @Override
                public void onShowAdComplete() {
                    Log.d(TAG, "App Open Ad show completed");
                    isAdLoaded = true;
                    checkAndNavigate();
                }
            });

            // Set a timeout for ad loading
            splashHandler.postDelayed(() -> {
                if (!isAdLoaded) {
                    Log.w(TAG, "App Open Ad timeout, proceeding without ad");
                    isAdLoaded = true;
                    checkAndNavigate();
                }
            }, Config.SPLASH_AD_TIMEOUT);
        } else {
            Log.w(TAG, "AppOpenAdManager not available");
            isAdLoaded = true;
            checkAndNavigate();
        }
    }

    /**
     * Check if both config and ad are loaded, then navigate
     */
    private void checkAndNavigate() {
        if (isConfigLoaded && isAdLoaded && !hasNavigated) {
            // Add minimum splash time
            splashHandler.postDelayed(navigationRunnable, SPLASH_SCREEN_TIMEOUT);
        }
    }

    /**
     * Navigate to the next activity
     */
    private void navigateToNextActivity() {
        if (hasNavigated) return;
        hasNavigated = true;

        Log.d(TAG, "Navigating to next activity");

        // Check if it's the user's first time opening the app
        SharedPreferences sharedPreferences = getSharedPreferences(Config.PREFS_APP_SETTINGS, MODE_PRIVATE);
        boolean isFirstTime = sharedPreferences.getBoolean("isFirstTime", true);

        Intent intent;
        if (isFirstTime) {
            // If first time, start Navigation_Activity (onboarding)
            intent = new Intent(Splash_Screen.this, Navigation_Activity.class);
            Log.d(TAG, "First time user, navigating to onboarding");
        } else {
            // If not first time, start MainActivity
            intent = new Intent(Splash_Screen.this, MainActivity.class);
            Log.d(TAG, "Returning user, navigating to main activity");
        }

        startActivity(intent);
        finish();
    }

    /**
     * Show maintenance dialog
     */
    private void showMaintenanceDialog(String message) {
        String displayMessage = message.isEmpty() ?
            "The app is currently under maintenance. Please try again later." : message;

        Toast.makeText(this, displayMessage, Toast.LENGTH_LONG).show();

        // Close app after showing maintenance message
        splashHandler.postDelayed(this::finish, 3000);
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (splashHandler != null) {
            splashHandler.removeCallbacks(navigationRunnable);
        }
    }
}